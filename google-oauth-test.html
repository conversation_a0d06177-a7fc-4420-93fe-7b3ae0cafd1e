<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .google-signin-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Google OAuth Configuration Test</h1>
        
        <div class="debug-info">
            <strong>Current Configuration:</strong><br>
            URL: <span id="current-url"></span><br>
            Client ID: <span id="client-id"></span><br>
            Origin: <span id="origin"></span>
        </div>

        <div class="step">
            <h3>📋 Google Cloud Console Settings:</h3>
            <p>Add these <strong>exact URLs</strong> to your Google Cloud Console:</p>
            <h4>Authorized JavaScript origins:</h4>
            <ul>
                <li>http://127.0.0.1:8080</li>
                <li>http://localhost:8080</li>
            </ul>
            <h4>Authorized redirect URIs:</h4>
            <ul>
                <li>http://127.0.0.1:8080</li>
                <li>http://localhost:8080</li>
                <li>http://127.0.0.1:8080/</li>
                <li>http://localhost:8080/</li>
            </ul>
        </div>
        
        <div class="google-signin-container">
            <h3>Google Sign-In Button Test</h3>
            <div id="g_id_onload"
                data-client_id="************-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com"
                data-callback="onGoogleSignIn"
                data-auto_prompt="false"
                data-ux_mode="popup"
                data-origin="http://127.0.0.1:8080">
            </div>
            <div class="d-flex justify-content-center">
                <div id="g_id_signin" 
                     class="g_id_signin" 
                     data-type="standard" 
                     data-size="large" 
                     data-theme="outline" 
                     data-text="sign_in_with" 
                     data-shape="rectangular" 
                     data-logo_alignment="left">
                </div>
            </div>
        </div>
        
        <div id="status-display" class="status info">
            <strong>Status:</strong> Waiting for Google Sign-In library to load...
        </div>
        
        <div id="error-display" class="status error" style="display: none;">
            <strong>Error:</strong> <span id="error-message"></span>
        </div>
        
        <div id="success-display" class="status success" style="display: none;">
            <strong>Success:</strong> <span id="success-message"></span>
        </div>

        <div class="debug-info">
            <strong>Debug Log:</strong>
            <div id="debug-log"></div>
        </div>
    </div>

    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script>
        // Update debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('client-id').textContent = '************-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com';
        document.getElementById('origin').textContent = window.location.origin;

        // Capture all network requests to see what Google is trying to access
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            addDebugLog('Fetch request: ' + args[0]);
            return originalFetch.apply(this, args);
        };

        // Monitor for any iframe or popup creation
        const originalOpen = window.open;
        window.open = function(...args) {
            addDebugLog('Window.open called with: ' + args[0]);
            return originalOpen.apply(this, args);
        };
        
        let debugLog = [];
        
        function addDebugLog(message) {
            debugLog.push(new Date().toLocaleTimeString() + ': ' + message);
            document.getElementById('debug-log').innerHTML = debugLog.join('<br>');
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            addDebugLog('Status: ' + message);
        }
        
        function showError(message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-display').style.display = 'block';
            addDebugLog('Error: ' + message);
        }
        
        function showSuccess(message) {
            document.getElementById('success-message').textContent = message;
            document.getElementById('success-display').style.display = 'block';
            addDebugLog('Success: ' + message);
        }
        
        // Check if Google Sign-In script loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (typeof google !== 'undefined' && google.accounts) {
                    showStatus('Google Sign-In library loaded successfully!', 'success');
                    addDebugLog('Google library available');
                } else {
                    showError('Google Sign-In library failed to load');
                }
            }, 2000);
        });
        
        // Google Sign In callback
        window.onGoogleSignIn = function(response) {
            addDebugLog('Google Sign-In callback triggered');
            
            if (response.credential) {
                try {
                    // Decode the JWT token
                    const payload = JSON.parse(atob(response.credential.split('.')[1]));
                    
                    showSuccess('Google Sign-In successful! User: ' + payload.email);
                    addDebugLog('User authenticated: ' + payload.email);
                    addDebugLog('User name: ' + payload.name);
                    
                    // Hide error display if it was shown
                    document.getElementById('error-display').style.display = 'none';
                    
                } catch (e) {
                    showError('Failed to decode Google response: ' + e.message);
                }
            } else {
                showError('Google Sign-In failed - no credential received');
            }
        };
        
        // Monitor for errors
        window.addEventListener('error', function(e) {
            addDebugLog('JavaScript error: ' + e.message);
        });
        
        addDebugLog('Test page initialized');
    </script>
</body>
</html>

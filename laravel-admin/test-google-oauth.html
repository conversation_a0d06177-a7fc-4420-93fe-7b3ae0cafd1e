<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .google-signin-container {
            text-align: center;
            margin: 30px 0;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google OAuth Configuration Test</h1>
        
        <div id="status" class="status info">
            Testing Google OAuth configuration...
        </div>
        
        <div class="debug-info">
            <strong>Current URL:</strong> <span id="current-url"></span><br>
            <strong>Client ID:</strong> <span id="client-id"></span><br>
            <strong>Google Script Loaded:</strong> <span id="script-loaded">Checking...</span>
        </div>
        
        <div class="google-signin-container">
            <div id="g_id_onload"
                data-client_id="************-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com"
                data-callback="onGoogleSignIn"
                data-auto_prompt="false"
                data-ux_mode="popup"
                data-origin="http://127.0.0.1:8080">
            </div>
            <div class="d-flex justify-content-center">
                <div id="g_id_signin" 
                     class="g_id_signin" 
                     data-type="standard" 
                     data-size="large" 
                     data-theme="outline" 
                     data-text="sign_in_with" 
                     data-shape="rectangular" 
                     data-logo_alignment="left">
                </div>
            </div>
        </div>
        
        <div id="error-display" class="status error" style="display: none;">
            <strong>Error:</strong> <span id="error-message"></span>
        </div>
        
        <div id="success-display" class="status success" style="display: none;">
            <strong>Success:</strong> <span id="success-message"></span>
        </div>
    </div>

    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script>
        // Update debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('client-id').textContent = '************-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com';
        
        // Check if Google script loaded
        setTimeout(() => {
            if (typeof google !== 'undefined' && google.accounts) {
                document.getElementById('script-loaded').textContent = 'Yes';
                document.getElementById('status').className = 'status success';
                document.getElementById('status').textContent = 'Google OAuth script loaded successfully!';
            } else {
                document.getElementById('script-loaded').textContent = 'No';
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = 'Google OAuth script failed to load. Check your internet connection and try again.';
            }
        }, 2000);
        
        // Google Sign In callback
        function onGoogleSignIn(response) {
            console.log('Google Sign-In response received:', response);
            
            if (response.credential) {
                try {
                    // Decode the JWT token
                    const payload = JSON.parse(atob(response.credential.split('.')[1]));
                    console.log('Decoded payload:', payload);
                    
                    // Show success
                    document.getElementById('success-display').style.display = 'block';
                    document.getElementById('success-message').textContent = `Successfully authenticated as ${payload.name} (${payload.email})`;
                    
                    // Hide error if it was showing
                    document.getElementById('error-display').style.display = 'none';
                    
                } catch (e) {
                    console.error('Token decode error:', e);
                    document.getElementById('error-display').style.display = 'block';
                    document.getElementById('error-message').textContent = 'Failed to decode authentication token: ' + e.message;
                }
            } else {
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-message').textContent = 'No credential received from Google';
            }
        }
        
        // Error handling
        window.addEventListener('error', function(e) {
            if (e.message.includes('Google') || e.message.includes('gsi')) {
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-message').textContent = 'Google OAuth error: ' + e.message;
            }
        });
        
        // Check for common issues
        window.addEventListener('load', function() {
            setTimeout(() => {
                const signinElement = document.querySelector('.g_id_signin');
                if (signinElement && signinElement.children.length === 0) {
                    document.getElementById('error-display').style.display = 'block';
                    document.getElementById('error-message').textContent = 'Google Sign-In button failed to render. This usually indicates an origin restriction issue. Please check your Google Cloud Console configuration.';
                }
            }, 3000);
        });
    </script>
</body>
</html> 
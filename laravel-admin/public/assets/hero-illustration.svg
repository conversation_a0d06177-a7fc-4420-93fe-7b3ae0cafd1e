<svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24" />
      <stop offset="100%" style="stop-color:#f59e0b" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff" />
      <stop offset="100%" style="stop-color:#f8fafc" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="500" height="400" fill="url(#bgGradient)" rx="20"/>
  
  <!-- Floating stars -->
  <g fill="url(#starGradient)">
    <path d="M50 80 L55 90 L65 90 L58 97 L60 107 L50 102 L40 107 L42 97 L35 90 L45 90 Z"/>
    <path d="M420 120 L425 130 L435 130 L428 137 L430 147 L420 142 L410 147 L412 137 L405 130 L415 130 Z"/>
    <path d="M380 200 L385 210 L395 210 L388 217 L390 227 L380 222 L370 227 L372 217 L365 210 L375 210 Z"/>
    <path d="M80 280 L85 290 L95 290 L88 297 L90 307 L80 302 L70 307 L72 297 L65 290 L75 290 Z"/>
  </g>
  
  <!-- Main illustration -->
  <g transform="translate(100, 80)">
    <!-- Review card -->
    <rect x="0" y="0" width="300" height="200" rx="15" fill="url(#cardGradient)" stroke="#e5e7eb" stroke-width="2"/>
    
    <!-- Card header -->
    <rect x="0" y="0" width="300" height="40" rx="15" fill="#6366f1"/>
    <text x="150" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Review Platform</text>
    
    <!-- Stars -->
    <g transform="translate(20, 60)">
      <path d="M0 0 L3 2 L6 0 L5 3 L6 6 L3 4 L0 6 L1 3 Z" fill="#fbbf24"/>
      <path d="M15 0 L18 2 L21 0 L20 3 L21 6 L18 4 L15 6 L16 3 Z" fill="#fbbf24"/>
      <path d="M30 0 L33 2 L36 0 L35 3 L36 6 L33 4 L30 6 L31 3 Z" fill="#fbbf24"/>
      <path d="M45 0 L48 2 L51 0 L50 3 L51 6 L48 4 L45 6 L46 3 Z" fill="#fbbf24"/>
      <path d="M60 0 L63 2 L66 0 L65 3 L66 6 L63 4 L60 6 L61 3 Z" fill="#fbbf24"/>
    </g>
    
    <!-- Review text -->
    <text x="20" y="100" font-family="Arial, sans-serif" font-size="12" fill="#374151">"Amazing service! The platform is</text>
    <text x="20" y="115" font-family="Arial, sans-serif" font-size="12" fill="#374151">user-friendly and payments are</text>
    <text x="20" y="130" font-family="Arial, sans-serif" font-size="12" fill="#374151">processed quickly. Highly</text>
    <text x="20" y="145" font-family="Arial, sans-serif" font-size="12" fill="#374151">recommended!"</text>
    
    <!-- User info -->
    <circle cx="250" cy="160" r="15" fill="#8b5cf6"/>
    <text x="250" y="165" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">U</text>
    <text x="270" y="165" font-family="Arial, sans-serif" font-size="10" fill="#6b7280">User123</text>
  </g>
  
  <!-- Money symbols -->
  <g transform="translate(350, 50)">
    <circle cx="0" cy="0" r="25" fill="#10b981" opacity="0.8"/>
    <text x="0" y="5" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" font-weight="bold">₹</text>
  </g>
  
  <g transform="translate(50, 300)">
    <circle cx="0" cy="0" r="20" fill="#f59e0b" opacity="0.8"/>
    <text x="0" y="5" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">₹</text>
  </g>
  
  <!-- Connection lines -->
  <g stroke="#6366f1" stroke-width="2" opacity="0.3" fill="none">
    <path d="M200 180 Q250 150 300 120"/>
    <path d="M150 200 Q100 250 50 280"/>
  </g>
</svg> 
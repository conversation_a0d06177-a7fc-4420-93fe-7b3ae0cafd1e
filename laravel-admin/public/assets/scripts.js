// assets/scripts.js

// Global variables
let selectedRole = 'customer';

// Smooth scroll for anchor links
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Navbar shrink on scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-shrink');
            } else {
                navbar.classList.remove('navbar-shrink');
            }
        });
    }

    // Initialize login modal functionality
    initializeLoginModal();
});

// Show login modal with role selection
window.showLoginModal = function(role = 'customer') {
    selectedRole = role;
    updateRoleButtons();
    
    // Update modal title based on role
    const modalTitle = document.getElementById('loginModalLabel');
    if (modalTitle) {
        modalTitle.textContent = role === 'reviewer' ? 'Start Earning as a Reviewer' : 'Get Reviews for Your Business';
    }
    
    var modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
};

// Select role function
window.selectRole = function(role) {
    selectedRole = role;
    updateRoleButtons();
    
    // Update modal title when role changes
    const modalTitle = document.getElementById('loginModalLabel');
    if (modalTitle) {
        modalTitle.textContent = role === 'reviewer' ? 'Start Earning as a Reviewer' : 'Get Reviews for Your Business';
    }
};

// Update role button states
function updateRoleButtons() {
    const customerBtn = document.getElementById('customerRoleBtn');
    const reviewerBtn = document.getElementById('reviewerRoleBtn');
    
    if (customerBtn && reviewerBtn) {
        // Remove active class from both buttons
        customerBtn.classList.remove('active', 'btn-dark');
        reviewerBtn.classList.remove('active', 'btn-dark');
        
        // Add active class to selected button
        if (selectedRole === 'customer') {
            customerBtn.classList.add('active', 'btn-dark');
        } else if (selectedRole === 'reviewer') {
            reviewerBtn.classList.add('active', 'btn-dark');
        }
    }
}

// Initialize login modal
function initializeLoginModal() {
    // Only set default role if no role is selected yet
    if (!selectedRole) {
        selectedRole = 'customer';
    }
    
    // Add click handlers for role buttons
    const customerBtn = document.getElementById('customerRoleBtn');
    const reviewerBtn = document.getElementById('reviewerRoleBtn');
    
    if (customerBtn) {
        customerBtn.addEventListener('click', function() {
            selectRole('customer');
        });
    }
    
    if (reviewerBtn) {
        reviewerBtn.addEventListener('click', function() {
            selectRole('reviewer');
        });
    }
    
    // Only update buttons if elements exist
    if (customerBtn && reviewerBtn) {
        updateRoleButtons();
    }
}

// Google Sign In callback
window.onGoogleSignIn = function(response) {
    if (response.credential) {
        try {
            // Decode the JWT token
            const payload = JSON.parse(atob(response.credential.split('.')[1]));
            
            // Ensure we have a valid role selected
            if (!selectedRole || !['customer', 'reviewer'].includes(selectedRole)) {
                selectedRole = 'customer'; // fallback
            }
            
            // Prepare data for server
            const userData = {
                email: payload.email,
                name: payload.name,
                picture: payload.picture,
                role: selectedRole,
                google_id: payload.sub // Use Google unique ID
            };
            
            // Send to server for processing
            fetch('/google_login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(userData)
            })
            .then(async response => {
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    
                    if (data.success && data.redirect) {
                        // Close modal before redirect
                        const modalElement = document.getElementById('loginModal');
                        if (modalElement) {
                            const modal = bootstrap.Modal.getInstance(modalElement);
                            if (modal) modal.hide();
                        }
                        
                        // Short delay to allow modal to close
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 100);
                    } else {
                        alert('Login failed: ' + (data.message || data.error || 'Unknown error'));
                    }
                } catch (e) {
                    // Only show debug info in development
                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                        console.error('JSON parse error:', e);
                        console.error('Raw response was:', text);
                    }
                    alert('Login failed. Please try again later.');
                }
            })
            .catch(error => {
                // Only show debug info in development
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    console.error('Fetch error:', error);
                }
                alert('Login failed. Please check your connection and try again.');
            });
        } catch (e) {
            // Only show debug info in development
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.error('Token decode error:', e);
            }
            alert('Authentication error. Please try again.');
        }
    } else {
        alert('Google Sign In failed. Please try again.');
    }
};

// Add smooth animations for cards
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe all cards
    document.querySelectorAll('.service-card, .step-card-modern, .benefit-card, .job-card-new').forEach(card => {
        observer.observe(card);
    });
});

// =================================
// REDESIGNED HOMEPAGE FUNCTIONALITY
// =================================

// Counter Animation
function animateCounter() {
    const counter = document.querySelector('.completed-counter');
    if (!counter) return;
    
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        counter.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// Service Type Selection
function initServiceTypeSelection() {
    document.querySelectorAll('.service-type-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.service-type-card').forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            
            // Store selected service type
            const serviceType = this.getAttribute('data-type');
            localStorage.setItem('selectedServiceType', serviceType);
        });
    });
}

// Country Flag Selection
function initCountryFlagSelection() {
    document.querySelectorAll('.flag-option').forEach(flag => {
        flag.addEventListener('click', function() {
            document.querySelectorAll('.flag-option').forEach(f => f.classList.remove('active'));
            this.classList.add('active');
            
            // Store selected country
            const country = this.getAttribute('data-country');
            localStorage.setItem('selectedCountry', country);
        });
    });
}

// Initialize animations when sections come into view
function initIntersectionObserver() {
    const observerOptions = {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target.classList.contains('reviews-completed-section')) {
                    animateCounter();
                }
            }
        });
    }, observerOptions);

    // Observe sections when available
    const completedSection = document.querySelector('.reviews-completed-section');
    if (completedSection) {
        observer.observe(completedSection);
    }
}

// Restore user selections from localStorage
function restoreUserSelections() {
    const selectedServiceType = localStorage.getItem('selectedServiceType');
    const selectedCountry = localStorage.getItem('selectedCountry');
    
    if (selectedServiceType) {
        const serviceCard = document.querySelector(`[data-type="${selectedServiceType}"]`);
        if (serviceCard) {
            serviceCard.classList.add('active');
        }
    }
    
    if (selectedCountry) {
        const countryFlag = document.querySelector(`[data-country="${selectedCountry}"]`);
        if (countryFlag) {
            countryFlag.classList.add('active');
        }
    }
}

// Initialize all homepage functionality
function initHomepageFeatures() {
    initServiceTypeSelection();
    initCountryFlagSelection();
    initIntersectionObserver();
    restoreUserSelections();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initHomepageFeatures();
}); 
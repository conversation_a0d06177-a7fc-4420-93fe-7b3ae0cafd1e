<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Debug - Port 8080</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .google-signin-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 10px;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .step-number {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 50%;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Google OAuth Debug - Port 8080</h1>
        
        <div id="status" class="status info">
            Testing Google OAuth configuration for http://127.0.0.1:8080...
        </div>
        
        <div class="debug-info">
            <strong>Current URL:</strong> <span id="current-url"></span><br>
            <strong>Client ID:</strong> <span id="client-id"></span><br>
            <strong>Google Script Loaded:</strong> <span id="script-loaded">Checking...</span><br>
            <strong>Button Rendered:</strong> <span id="button-rendered">Checking...</span><br>
            <strong>Error Details:</strong> <span id="error-details">None</span>
        </div>
        
        <div class="google-signin-container">
            <h3>Google Sign-In Button Test</h3>
            <div id="g_id_onload"
                data-client_id="************-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com"
                data-callback="onGoogleSignIn"
                data-auto_prompt="false"
                data-ux_mode="popup"
                data-origin="http://127.0.0.1:8080">
            </div>
            <div class="d-flex justify-content-center">
                <div id="g_id_signin" 
                     class="g_id_signin" 
                     data-type="standard" 
                     data-size="large" 
                     data-theme="outline" 
                     data-text="sign_in_with" 
                     data-shape="rectangular" 
                     data-logo_alignment="left">
                </div>
            </div>
        </div>
        
        <div id="error-display" class="status error" style="display: none;">
            <strong>Error:</strong> <span id="error-message"></span>
        </div>
        
        <div id="success-display" class="status success" style="display: none;">
            <strong>Success:</strong> <span id="success-message"></span>
        </div>
        
        <div class="instructions">
            <h3>🔧 How to Fix Google OAuth Origin Restrictions</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Go to Google Cloud Console:</strong> <a href="https://console.cloud.google.com/" target="_blank">https://console.cloud.google.com/</a>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Select your project</strong> (the one with client ID: 668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com)
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Navigate to:</strong> APIs & Services > Credentials
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Click on your OAuth 2.0 client ID</strong> to edit it
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>Add to "Authorized JavaScript origins":</strong><br>
                <code>http://127.0.0.1:8080</code><br>
                <code>http://localhost:8080</code>
            </div>
            
            <div class="step">
                <span class="step-number">6</span>
                <strong>Add to "Authorized redirect URIs":</strong><br>
                <code>http://127.0.0.1:8080/auth/google/callback</code><br>
                <code>http://localhost:8080/auth/google/callback</code>
            </div>
            
            <div class="step">
                <span class="step-number">7</span>
                <strong>Click Save</strong> and wait 5-10 minutes for changes to propagate
            </div>
            
            <div class="step">
                <span class="step-number">8</span>
                <strong>Refresh this page</strong> to test the fix
            </div>
        </div>
    </div>

    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script>
        // Update debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('client-id').textContent = '************-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com';
        
        let googleScriptLoaded = false;
        let buttonRendered = false;
        let errorDetails = [];
        
        // Check if Google script loaded
        setTimeout(() => {
            if (typeof google !== 'undefined' && google.accounts) {
                googleScriptLoaded = true;
                document.getElementById('script-loaded').textContent = 'Yes';
                document.getElementById('status').className = 'status success';
                document.getElementById('status').textContent = 'Google OAuth script loaded successfully!';
            } else {
                document.getElementById('script-loaded').textContent = 'No';
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = 'Google OAuth script failed to load. Check your internet connection.';
                errorDetails.push('Google script not loaded');
            }
            updateErrorDetails();
        }, 2000);
        
        // Check if button rendered
        setTimeout(() => {
            const signinElement = document.querySelector('.g_id_signin');
            if (signinElement && signinElement.children.length > 0) {
                buttonRendered = true;
                document.getElementById('button-rendered').textContent = 'Yes';
                if (googleScriptLoaded) {
                    document.getElementById('status').className = 'status success';
                    document.getElementById('status').textContent = 'Google OAuth is working correctly!';
                }
            } else {
                document.getElementById('button-rendered').textContent = 'No';
                if (googleScriptLoaded) {
                    document.getElementById('status').className = 'status warning';
                    document.getElementById('status').textContent = 'Google script loaded but button not rendered. This indicates an origin restriction issue.';
                    errorDetails.push('Button not rendered - origin restriction likely');
                }
            }
            updateErrorDetails();
        }, 3000);
        
        function updateErrorDetails() {
            document.getElementById('error-details').textContent = errorDetails.length > 0 ? errorDetails.join(', ') : 'None';
        }
        
        // Google Sign In callback
        function onGoogleSignIn(response) {
            console.log('Google Sign-In response received:', response);
            
            if (response.credential) {
                try {
                    // Decode the JWT token
                    const payload = JSON.parse(atob(response.credential.split('.')[1]));
                    console.log('Decoded payload:', payload);
                    
                    // Show success
                    document.getElementById('success-display').style.display = 'block';
                    document.getElementById('success-message').textContent = `Successfully authenticated as ${payload.name} (${payload.email})`;
                    
                    // Hide error if it was showing
                    document.getElementById('error-display').style.display = 'none';
                    
                } catch (e) {
                    console.error('Token decode error:', e);
                    document.getElementById('error-display').style.display = 'block';
                    document.getElementById('error-message').textContent = 'Failed to decode authentication token: ' + e.message;
                }
            } else {
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-message').textContent = 'No credential received from Google';
            }
        }
        
        // Error handling
        window.addEventListener('error', function(e) {
            if (e.message.includes('Google') || e.message.includes('gsi') || e.message.includes('accounts.google.com')) {
                errorDetails.push('JavaScript error: ' + e.message);
                updateErrorDetails();
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-message').textContent = 'Google OAuth error: ' + e.message;
            }
        });
        
        // Additional error checking
        window.addEventListener('message', function(event) {
            if (event.origin !== 'https://accounts.google.com') {
                return;
            }
            
            console.log('Google OAuth message received:', event.data);
            
            if (event.data.type === 'error') {
                console.error('Google OAuth error:', event.data);
                errorDetails.push('OAuth error: ' + (event.data.message || 'Unknown error'));
                updateErrorDetails();
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-message').textContent = 'Google authentication error: ' + (event.data.message || 'Unknown error');
            }
        });
    </script>
</body>
</html> 
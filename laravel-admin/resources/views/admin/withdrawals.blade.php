@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="navbar-brand" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-shield-alt me-2"></i>
                            Admin Panel
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.users') }}">
                            <i class="fas fa-users me-2"></i>
                            Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.reviews') }}">
                            <i class="fas fa-star me-2"></i>
                            Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.withdrawals') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Withdrawals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.settings') }}">
                            <i class="fas fa-cog me-2"></i>
                            Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="nav-link border-0 bg-transparent">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Withdrawals Management</h1>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Withdrawals Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">All Withdrawal Requests</h5>
                </div>
                <div class="card-body">
                    @if($withdrawals->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Requested</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($withdrawals as $withdrawal)
                                    <tr>
                                        <td>{{ $withdrawal->id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($withdrawal->user->profile_picture)
                                                    <img src="{{ $withdrawal->user->profile_picture }}" class="rounded-circle me-2" width="32" height="32" alt="Profile">
                                                @else
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                                        <span class="text-white fw-bold">{{ strtoupper(substr($withdrawal->user->name, 0, 1)) }}</span>
                                                    </div>
                                                @endif
                                                <div>
                                                    <strong>{{ $withdrawal->user->name }}</strong><br>
                                                    <small class="text-muted">{{ $withdrawal->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="fw-bold">₹{{ number_format($withdrawal->amount, 2) }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($withdrawal->payment_method) }}</span>
                                        </td>
                                        <td>
                                            @if($withdrawal->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($withdrawal->status === 'approved')
                                                <span class="badge bg-success">Approved</span>
                                            @elseif($withdrawal->status === 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($withdrawal->status) }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $withdrawal->created_at->format('M d, Y H:i') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#withdrawalModal{{ $withdrawal->id }}">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($withdrawal->status === 'pending')
                                                <button class="btn btn-sm btn-outline-success" onclick="approveWithdrawal({{ $withdrawal->id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="rejectWithdrawal({{ $withdrawal->id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center">
                            {{ $withdrawals->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No withdrawal requests found</h5>
                            <p class="text-muted">No withdrawal requests have been submitted yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawal Detail Modals -->
@foreach($withdrawals as $withdrawal)
<div class="modal fade" id="withdrawalModal{{ $withdrawal->id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdrawal Details - {{ $withdrawal->user->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>User Information</h6>
                        <p><strong>Name:</strong> {{ $withdrawal->user->name }}</p>
                        <p><strong>Email:</strong> {{ $withdrawal->user->email }}</p>
                        <p><strong>Role:</strong> {{ ucfirst($withdrawal->user->role) }}</p>
                        <p><strong>Current Balance:</strong> ₹{{ number_format($withdrawal->user->wallet_balance, 2) }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Withdrawal Information</h6>
                        <p><strong>Amount:</strong> ₹{{ number_format($withdrawal->amount, 2) }}</p>
                        <p><strong>Payment Method:</strong> {{ ucfirst($withdrawal->payment_method) }}</p>
                        <p><strong>Status:</strong> {{ ucfirst($withdrawal->status) }}</p>
                        <p><strong>Requested:</strong> {{ $withdrawal->created_at->format('M d, Y H:i') }}</p>
                        @if($withdrawal->processed_at)
                        <p><strong>Processed:</strong> {{ $withdrawal->processed_at->format('M d, Y H:i') }}</p>
                        @endif
                    </div>
                </div>
                <hr>
                <div>
                    <h6>Payment Details</h6>
                    <p>{{ $withdrawal->payment_details }}</p>
                </div>
                @if($withdrawal->admin_notes)
                <hr>
                <div>
                    <h6>Admin Notes</h6>
                    <p>{{ $withdrawal->admin_notes }}</p>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endforeach

<script>
function approveWithdrawal(withdrawalId) {
    if (confirm('Are you sure you want to approve this withdrawal request?')) {
        fetch(`/admin/approve-withdrawal/${withdrawalId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while approving the withdrawal.');
        });
    }
}

function rejectWithdrawal(withdrawalId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason !== null) {
        fetch(`/admin/reject-withdrawal/${withdrawalId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while rejecting the withdrawal.');
        });
    }
}
</script>
@endsection 
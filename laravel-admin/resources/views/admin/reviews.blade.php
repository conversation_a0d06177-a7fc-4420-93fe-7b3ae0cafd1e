@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="navbar-brand" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-shield-alt me-2"></i>
                            Admin Panel
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.users') }}">
                            <i class="fas fa-users me-2"></i>
                            Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.reviews') }}">
                            <i class="fas fa-star me-2"></i>
                            Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.withdrawals') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Withdrawals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.settings') }}">
                            <i class="fas fa-cog me-2"></i>
                            Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="nav-link border-0 bg-transparent">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Reviews Management</h1>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Reviews Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">All Reviews</h5>
                </div>
                <div class="card-body">
                    @if($reviews->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Business</th>
                                        <th>Platform</th>
                                        <th>Customer</th>
                                        <th>Reviewer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reviews as $review)
                                    <tr>
                                        <td>{{ $review->id }}</td>
                                        <td>
                                            <strong>{{ $review->business_name }}</strong><br>
                                            <small class="text-muted">{{ Str::limit($review->business_url, 30) }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $review->platform }}</span>
                                        </td>
                                        <td>
                                            @if($review->customer)
                                                {{ $review->customer->name }}
                                            @else
                                                <span class="text-muted">Unknown</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($review->reviewer)
                                                {{ $review->reviewer->name }}
                                            @else
                                                <span class="text-muted">Not claimed</span>
                                            @endif
                                        </td>
                                        <td>₹{{ number_format($review->amount, 2) }}</td>
                                        <td>
                                            @if($review->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($review->status === 'approved')
                                                <span class="badge bg-success">Approved</span>
                                            @elseif($review->status === 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @elseif($review->status === 'claimed')
                                                <span class="badge bg-info">Claimed</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($review->status) }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $review->created_at->format('M d, Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reviewModal{{ $review->id }}">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($review->status === 'pending')
                                                <button class="btn btn-sm btn-outline-success" onclick="approveReview({{ $review->id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="rejectReview({{ $review->id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center">
                            {{ $reviews->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No reviews found</h5>
                            <p class="text-muted">No reviews have been posted yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Detail Modals -->
@foreach($reviews as $review)
<div class="modal fade" id="reviewModal{{ $review->id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Details - {{ $review->business_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Review Information</h6>
                        <p><strong>Business:</strong> {{ $review->business_name }}</p>
                        <p><strong>Platform:</strong> {{ $review->platform }}</p>
                        <p><strong>URL:</strong> <a href="{{ $review->business_url }}" target="_blank">{{ $review->business_url }}</a></p>
                        <p><strong>Amount:</strong> ₹{{ number_format($review->amount, 2) }}</p>
                        <p><strong>Status:</strong> {{ ucfirst($review->status) }}</p>
                        <p><strong>Created:</strong> {{ $review->created_at->format('M d, Y H:i') }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>User Information</h6>
                        <p><strong>Customer:</strong> {{ $review->customer->name ?? 'Unknown' }}</p>
                        <p><strong>Customer Email:</strong> {{ $review->customer->email ?? 'Unknown' }}</p>
                        @if($review->reviewer)
                        <p><strong>Reviewer:</strong> {{ $review->reviewer->name }}</p>
                        <p><strong>Reviewer Email:</strong> {{ $review->reviewer->email }}</p>
                        <p><strong>Claimed:</strong> {{ $review->claimed_at ? $review->claimed_at->format('M d, Y H:i') : 'N/A' }}</p>
                        @endif
                    </div>
                </div>
                <hr>
                <div>
                    <h6>Review Text</h6>
                    <p>{{ $review->review_text }}</p>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Screenshot</h6>
                        <img src="{{ Storage::url($review->screenshot_path) }}" class="img-fluid" alt="Review Screenshot">
                    </div>
                    <div class="col-md-6">
                        <h6>UPI QR Code</h6>
                        <img src="{{ Storage::url($review->upi_qr_path) }}" class="img-fluid" alt="UPI QR Code">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endforeach

<script>
function approveReview(reviewId) {
    if (confirm('Are you sure you want to approve this review?')) {
        fetch(`/admin/approve-review/${reviewId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while approving the review.');
        });
    }
}

function rejectReview(reviewId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason !== null) {
        fetch(`/admin/reject-review/${reviewId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while rejecting the review.');
        });
    }
}
</script>
@endsection 
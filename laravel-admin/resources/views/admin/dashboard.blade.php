@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('content')
<div class="page-content">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-shield-alt text-primary me-2"></i>Admin Dashboard
                        </h1>
                        <p class="text-muted mb-0">Manage your platform from here</p>
                    </div>
                    <div class="d-none d-md-block">
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-success">System Online</span>
                            <span class="text-muted small">Last updated: {{ now()->format('M j, H:i') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="h4 mb-1">{{ number_format($stats['total_users'] ?? 0) }}</div>
                                <small class="text-muted">Total Users</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-arrow-up me-1"></i>
                                +{{ $stats['new_users_today'] ?? 0 }} today
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="h4 mb-1">{{ number_format($stats['total_reviews'] ?? 0) }}</div>
                                <small class="text-muted">Total Reviews</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ $stats['pending_reviews'] ?? 0 }} pending
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="h4 mb-1">${{ number_format($stats['total_revenue'] ?? 0, 2) }}</div>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-chart-line me-1"></i>
                                +${{ number_format($stats['revenue_today'] ?? 0, 2) }} today
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="h4 mb-1">{{ number_format($stats['pending_withdrawals'] ?? 0) }}</div>
                                <small class="text-muted">Pending Withdrawals</small>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Needs attention
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Recent Activity -->
            <div class="col-lg-8">
                <!-- Analytics Chart Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Platform Performance
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" data-period="7">7 Days</button>
                            <button class="btn btn-outline-secondary" data-period="30">30 Days</button>
                            <button class="btn btn-outline-secondary" data-period="90">90 Days</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Chart Container -->
                        <div class="chart-container" style="height: 300px; position: relative;">
                            <canvas id="performanceChart"></canvas>
                        </div>
                        
                        <!-- Chart Legend -->
                        <div class="chart-legend mt-3">
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="legend-item">
                                        <div class="legend-indicator" style="background: var(--dark);"></div>
                                        <small class="text-muted">Reviews</small>
                                        <div class="h6 mb-0">{{ $stats['total_reviews'] ?? 0 }}</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="legend-item">
                                        <div class="legend-indicator" style="background: var(--gray-600);"></div>
                                        <small class="text-muted">Users</small>
                                        <div class="h6 mb-0">{{ $stats['total_users'] ?? 0 }}</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="legend-item">
                                        <div class="legend-indicator" style="background: var(--gray-400);"></div>
                                        <small class="text-muted">Revenue</small>
                                        <div class="h6 mb-0">${{ number_format($stats['total_revenue'] ?? 0, 0) }}</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="legend-item">
                                        <div class="legend-indicator" style="background: var(--gray-300);"></div>
                                        <small class="text-muted">Avg. Rating</small>
                                        <div class="h6 mb-0">4.8</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Metrics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>Current Goals
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="progress-metric mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm font-medium">Monthly Review Target</span>
                                <span class="text-sm text-muted">{{ $stats['completed_reviews'] ?? 0 }}/500</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar" data-progress="{{ min(100, (($stats['completed_reviews'] ?? 0) / 500) * 100) }}"></div>
                            </div>
                            <div class="progress-details mt-2">
                                <small class="text-muted">{{ number_format(min(100, (($stats['completed_reviews'] ?? 0) / 500) * 100), 1) }}% complete</small>
                            </div>
                        </div>
                        
                        <div class="progress-metric mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm font-medium">User Growth Target</span>
                                <span class="text-sm text-muted">{{ $stats['total_users'] ?? 0 }}/1000</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar" data-progress="{{ min(100, (($stats['total_users'] ?? 0) / 1000) * 100) }}"></div>
                            </div>
                            <div class="progress-details mt-2">
                                <small class="text-muted">{{ number_format(min(100, (($stats['total_users'] ?? 0) / 1000) * 100), 1) }}% complete</small>
                            </div>
                        </div>
                        
                        <div class="progress-metric">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-sm font-medium">Revenue Goal</span>
                                <span class="text-sm text-muted">${{ number_format($stats['total_revenue'] ?? 0, 0) }}/$10,000</span>
                            </div>
                            <div class="progress-container">
                                <div class="progress-bar" data-progress="{{ min(100, (($stats['total_revenue'] ?? 0) / 10000) * 100) }}"></div>
                            </div>
                            <div class="progress-details mt-2">
                                <small class="text-muted">{{ number_format(min(100, (($stats['total_revenue'] ?? 0) / 10000) * 100), 1) }}% complete</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">
                            <i class="fas fa-activity me-2"></i>Recent Activity
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" id="refresh-activity">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body p-0">
                        @if(isset($recent_activities) && $recent_activities->count() > 0)
                            @foreach($recent_activities as $activity)
                            <div class="activity-timeline-item">
                                <div class="activity-indicator">
                                    @if($activity->type === 'review')
                                        <i class="fas fa-star"></i>
                                    @elseif($activity->type === 'user')
                                        <i class="fas fa-user"></i>
                                    @elseif($activity->type === 'withdrawal')
                                        <i class="fas fa-money-bill"></i>
                                    @else
                                        <i class="fas fa-bell"></i>
                                    @endif
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">{{ $activity->title ?? 'Activity' }}</div>
                                    <div class="activity-description">{{ $activity->description ?? 'Description' }}</div>
                                    <div class="activity-time">{{ $activity->created_at->diffForHumans() }}</div>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <p>No recent activity</p>
                            </div>
                        @endif
                    </div>
                    <div class="card-footer">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            View All Activity
                        </a>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4 col-6">
                                <a href="{{ route('admin.users') }}" class="quick-action-btn">
                                    <i class="fas fa-users"></i>
                                    <span>Manage Users</span>
                                </a>
                            </div>
                            <div class="col-md-4 col-6">
                                <a href="{{ route('admin.reviews') }}" class="quick-action-btn">
                                    <i class="fas fa-star"></i>
                                    <span>Review Queue</span>
                                </a>
                            </div>
                            <div class="col-md-4 col-6">
                                <a href="{{ route('admin.withdrawals') }}" class="quick-action-btn">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Withdrawals</span>
                                </a>
                            </div>
                            <div class="col-md-4 col-6">
                                <button class="quick-action-btn" onclick="exportData()">
                                    <i class="fas fa-download"></i>
                                    <span>Export Data</span>
                                </button>
                            </div>
                            <div class="col-md-4 col-6">
                                <button class="quick-action-btn" onclick="showSettings()">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </button>
                            </div>
                            <div class="col-md-4 col-6">
                                <button class="quick-action-btn" onclick="generateReport()">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Generate Report</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- System Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-server text-success me-2"></i>System Status
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <span class="small">Database</span>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Online
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <span class="small">Payment Gateway</span>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Connected
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <span class="small">Email Service</span>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Active
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-between">
                            <span class="small">Storage</span>
                            <span class="badge bg-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>75% Full
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Recent Reviews -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>Recent Reviews
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        @if(isset($recent_reviews) && $recent_reviews->count() > 0)
                            @foreach($recent_reviews->take(5) as $review)
                            <div class="d-flex align-items-center p-3 border-bottom">
                                <div class="flex-shrink-0 me-3">
                                    <div class="avatar-sm rounded-circle bg-light d-flex align-items-center justify-content-center">
                                        @if($review->platform === 'Google')
                                            <i class="fab fa-google text-danger"></i>
                                        @elseif($review->platform === 'Trustpilot')
                                            <i class="fas fa-star text-warning"></i>
                                        @else
                                            <i class="fas fa-globe text-secondary"></i>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-semibold small">{{ Str::limit($review->business_name ?? 'Business', 15) }}</div>
                                    <div class="small text-muted">{{ $review->platform }}</div>
                                </div>
                                <div class="text-end">
                                    <span class="badge {{ $review->status === 'pending' ? 'bg-warning' : ($review->status === 'completed' ? 'bg-success' : 'bg-secondary') }}">
                                        {{ ucfirst($review->status ?? 'pending') }}
                                    </span>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="p-3 text-center text-muted">
                                <i class="fas fa-star fa-2x mb-2 opacity-50"></i>
                                <p class="mb-0 small">No recent reviews</p>
                            </div>
                        @endif
                    </div>
                    <div class="card-footer">
                        <a href="{{ route('admin.reviews') }}" class="btn btn-outline-primary btn-sm w-100">
                            View All Reviews
                        </a>
                    </div>
                </div>

                <!-- Platform Analytics -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie text-info me-2"></i>Platform Breakdown
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="fw-semibold">Google Reviews</small>
                                <small class="text-muted">{{ $platform_stats['google'] ?? 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-danger" style="width: {{ $platform_stats['google'] ?? 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="fw-semibold">Trustpilot</small>
                                <small class="text-muted">{{ $platform_stats['trustpilot'] ?? 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: {{ $platform_stats['trustpilot'] ?? 0 }}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="fw-semibold">Facebook</small>
                                <small class="text-muted">{{ $platform_stats['facebook'] ?? 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-primary" style="width: {{ $platform_stats['facebook'] ?? 0 }}%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="fw-semibold">Others</small>
                                <small class="text-muted">{{ $platform_stats['others'] ?? 0 }}%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-secondary" style="width: {{ $platform_stats['others'] ?? 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Enhanced Admin Dashboard Styles */
.stat-card:hover .stat-icon {
    background-color: var(--gray-200);
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 300px;
    margin: var(--space-4) 0;
}

.chart-legend {
    padding-top: var(--space-4);
    border-top: var(--border-width) solid var(--border-color);
}

.legend-item {
    text-align: center;
}

.legend-indicator {
    width: 12px;
    height: 12px;
    border-radius: var(--border-radius-full);
    margin: 0 auto var(--space-2);
    opacity: 0.8;
}

/* Progress Metrics */
.progress-metric {
    padding: var(--space-4) 0;
    border-bottom: var(--border-width) solid var(--gray-200);
}

.progress-metric:last-child {
    border-bottom: none;
}

.progress-container {
    height: 8px;
    background-color: var(--gray-200);
    border-radius: var(--border-radius-full);
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--dark) 0%, var(--gray-600) 100%);
    border-radius: var(--border-radius-full);
    width: 0%;
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 6px;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.1) 2px,
        rgba(255, 255, 255, 0.1) 4px
    );
}

.progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Activity Timeline */
.activity-timeline-item {
    display: flex;
    align-items: flex-start;
    padding: var(--space-4) var(--space-6);
    border-bottom: var(--border-width) solid var(--gray-200);
    transition: var(--transition-all);
}

.activity-timeline-item:hover {
    background-color: var(--gray-25);
}

.activity-timeline-item:last-child {
    border-bottom: none;
}

.activity-indicator {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-full);
    background-color: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-right: var(--space-4);
    border: 2px solid var(--white);
    box-shadow: var(--shadow-sm);
}

.activity-indicator i {
    font-size: var(--text-sm);
    color: var(--gray-700);
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-weight: var(--font-weight-semibold);
    color: var(--dark);
    font-size: var(--text-sm);
    margin-bottom: var(--space-1);
}

.activity-description {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-1);
}

.activity-time {
    color: var(--gray-500);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
}

/* Quick Actions */
.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--gray-700);
    background-color: var(--white);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--space-6) var(--space-4);
    transition: var(--transition-all);
    min-height: 100px;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    color: var(--dark);
    background-color: var(--gray-50);
    border-color: var(--border-color-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.quick-action-btn i {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-3);
}

.quick-action-btn span {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--gray-500);
}

.empty-state i {
    font-size: var(--text-5xl);
    margin-bottom: var(--space-4);
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: var(--text-sm);
}

/* Button Group Enhancements */
.btn-group .btn.active {
    background-color: var(--dark);
    border-color: var(--dark);
    color: var(--white);
}

.btn-group .btn:not(.active):hover {
    background-color: var(--gray-100);
    border-color: var(--border-color-dark);
}

/* Card Header Enhancements */
.card-header h5 {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
}

.card-header h5 i {
    color: var(--gray-600);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }
    
    .legend-item {
        margin-bottom: var(--space-3);
    }
    
    .quick-action-btn {
        min-height: 80px;
        padding: var(--space-4) var(--space-2);
    }
    
    .quick-action-btn i {
        font-size: var(--text-xl);
        margin-bottom: var(--space-2);
    }
    
    .activity-timeline-item {
        padding: var(--space-3) var(--space-4);
    }
    
    .activity-indicator {
        width: 28px;
        height: 28px;
        margin-right: var(--space-3);
    }
}
</style>
@endpush

@push('scripts')
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Progress Bars
    initializeProgressBars();
    
    // Initialize Chart
    initializePerformanceChart();
    
    // Add chart period toggle functionality
    document.querySelectorAll('[data-period]').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateChartData(this.dataset.period);
        });
    });
});

function initializeProgressBars() {
    document.querySelectorAll('.progress-bar').forEach(bar => {
        const progress = parseFloat(bar.dataset.progress) || 0;
        
        // Animate progress bar
        setTimeout(() => {
            bar.style.width = progress + '%';
        }, 300);
        
        // Add pattern animation for active progress
        if (progress > 0) {
            bar.style.backgroundImage = `
                linear-gradient(90deg, var(--dark) 0%, var(--gray-600) 100%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 10px,
                    rgba(255, 255, 255, 0.1) 10px,
                    rgba(255, 255, 255, 0.1) 20px
                )
            `;
        }
    });
}

function initializePerformanceChart() {
    const ctx = document.getElementById('performanceChart');
    if (!ctx) return;
    
    // Sample data - replace with real data from backend
    const data = {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
            {
                label: 'Reviews',
                data: [12, 19, 8, 15, 22, 18, 25],
                borderColor: '#000000',
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 4,
                pointHoverRadius: 6,
                pointBackgroundColor: '#000000',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2
            },
            {
                label: 'Users',
                data: [8, 12, 15, 10, 18, 14, 20],
                borderColor: '#6c757d',
                backgroundColor: 'rgba(108, 117, 125, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 4,
                pointHoverRadius: 6,
                pointBackgroundColor: '#6c757d',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2
            },
            {
                label: 'Revenue',
                data: [150, 280, 180, 220, 350, 290, 400],
                borderColor: '#ced4da',
                backgroundColor: 'rgba(206, 212, 218, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5,
                pointBackgroundColor: '#ced4da',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                borderDash: [5, 5]
            }
        ]
    };
    
    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: '#000000',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#6c757d',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.dataset.label === 'Revenue') {
                                label += '$' + context.parsed.y;
                            } else {
                                label += context.parsed.y;
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: '#e9ecef',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                y: {
                    grid: {
                        color: '#e9ecef',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#6c757d',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutCubic'
            }
        }
    };
    
    window.performanceChart = new Chart(ctx, config);
}

function updateChartData(period) {
    if (!window.performanceChart) return;
    
    // Sample data for different periods
    const dataMap = {
        '7': {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            reviews: [12, 19, 8, 15, 22, 18, 25],
            users: [8, 12, 15, 10, 18, 14, 20],
            revenue: [150, 280, 180, 220, 350, 290, 400]
        },
        '30': {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            reviews: [85, 92, 78, 105],
            users: [65, 72, 68, 85],
            revenue: [1200, 1500, 1100, 1800]
        },
        '90': {
            labels: ['Month 1', 'Month 2', 'Month 3'],
            reviews: [340, 385, 420],
            users: [290, 320, 365],
            revenue: [5200, 6100, 7300]
        }
    };
    
    const newData = dataMap[period];
    if (newData) {
        window.performanceChart.data.labels = newData.labels;
        window.performanceChart.data.datasets[0].data = newData.reviews;
        window.performanceChart.data.datasets[1].data = newData.users;
        window.performanceChart.data.datasets[2].data = newData.revenue;
        window.performanceChart.update('active');
    }
}

// Enhanced Admin Functions
function refreshActivity() {
    const button = document.getElementById('refresh-activity');
    LoadingManager.show(button, 'Refreshing...');
    
    // Simulate API call
    setTimeout(() => {
        LoadingManager.hide(button);
        showToast('Activity refreshed successfully!', 'success');
        
        // Add a subtle animation to activity items
        document.querySelectorAll('.activity-timeline-item').forEach((item, index) => {
            item.style.animation = `fadeInUp 0.3s ease-out ${index * 0.1}s both`;
        });
    }, 1500);
}

function exportData() {
    showToast('Preparing data export...', 'info');
    
    // Simulate export process
    setTimeout(() => {
        showToast('Data exported successfully!', 'success');
    }, 2000);
}

function showSettings() {
    showToast('Settings panel opening...', 'info');
}

function generateReport() {
    showToast('Generating comprehensive report...', 'info');
    
    // Simulate report generation
    setTimeout(() => {
        showToast('Report generated successfully!', 'success');
    }, 3000);
}
</script>
@endpush
@endsection 
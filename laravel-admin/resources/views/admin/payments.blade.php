@extends('layouts.app')

@section('title', 'Payment Management')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-money-check-alt text-primary me-2"></i>Payment Management
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success" onclick="refreshPage()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportData()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Pending Verification</h6>
                            <h2 class="mb-0">{{ $stats['pending_count'] }}</h2>
                            <small class="text-white-75">₹{{ number_format($stats['total_pending_amount'], 2) }}</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hourglass-half fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Approved Today</h6>
                            <h2 class="mb-0">{{ $stats['approved_today'] }}</h2>
                            <small class="text-white-75">Payments</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Total Approved</h6>
                            <h2 class="mb-0">₹{{ number_format($stats['total_approved_amount'], 2) }}</h2>
                            <small class="text-white-75">All time</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Processing Time</h6>
                            <h2 class="mb-0">< 24h</h2>
                            <small class="text-white-75">Average</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payments Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>Pending Payment Verification
                        <span class="badge bg-warning ms-2">{{ $pendingPayments->total() }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if($pendingPayments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Transaction ID</th>
                                        <th>Submitted</th>
                                        <th>Proof</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingPayments as $payment)
                                    <tr>
                                        <td>
                                            <span class="font-monospace text-primary">#{{ $payment->order_id }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">
                                                    {{ strtoupper(substr($payment->user->name, 0, 1)) }}
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ $payment->user->name }}</div>
                                                    <small class="text-muted">{{ $payment->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">₹{{ number_format($payment->amount, 2) }}</span>
                                        </td>
                                        <td>
                                            <span class="font-monospace">{{ $payment->transaction_id }}</span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $payment->submitted_at->format('M d, Y') }}</span>
                                            <br>
                                            <small class="text-muted">{{ $payment->submitted_at->format('H:i A') }}</small>
                                        </td>
                                        <td>
                                            @if($payment->payment_screenshot)
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewProof('{{ $payment->id }}')">
                                                    <i class="fas fa-image me-1"></i>View Proof
                                                </button>
                                            @else
                                                <span class="text-muted">No proof</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-success" onclick="approvePayment({{ $payment->id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="rejectPayment({{ $payment->id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <button class="btn btn-sm btn-info" onclick="viewDetails({{ $payment->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        {{ $pendingPayments->links() }}
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h6 class="text-muted">All caught up!</h6>
                            <p class="text-muted">No pending payment verifications at the moment.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>Recently Approved
                    </h6>
                </div>
                <div class="card-body">
                    @if($recentApproved->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentApproved as $payment)
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div>
                                    <div class="fw-semibold">#{{ $payment->order_id }}</div>
                                    <small class="text-muted">{{ $payment->user->name }} • ₹{{ $payment->amount }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">Approved</span>
                                    <br>
                                    <small class="text-muted">{{ $payment->approved_at->diffForHumans() }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent approvals</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-times text-danger me-2"></i>Recently Rejected
                    </h6>
                </div>
                <div class="card-body">
                    @if($recentRejected->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentRejected as $payment)
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div>
                                    <div class="fw-semibold">#{{ $payment->order_id }}</div>
                                    <small class="text-muted">{{ $payment->user->name }} • ₹{{ $payment->amount }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-danger">Rejected</span>
                                    <br>
                                    <small class="text-muted">{{ $payment->rejected_at->diffForHumans() }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No recent rejections</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Proof Modal -->
<div class="modal fade" id="proofModal" tabindex="-1" aria-labelledby="proofModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="proofModalLabel">
                    <i class="fas fa-image text-primary me-2"></i>Payment Proof
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="proofImage" src="" alt="Payment Proof" class="img-fluid rounded shadow">
                <div id="proofDetails" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="fas fa-info-circle text-info me-2"></i>Payment Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="paymentDetails"></div>
            </div>
        </div>
    </div>
</div>

<!-- Approve Payment Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">
                    <i class="fas fa-check-circle text-success me-2"></i>Approve Payment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="approveForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This will credit the amount to customer's wallet and mark payment as approved.
                    </div>
                    <div class="mb-3">
                        <label for="adminNote" class="form-label">Admin Note (Optional)</label>
                        <textarea class="form-control" id="adminNote" name="admin_note" rows="3" 
                                  placeholder="Add any additional notes..."></textarea>
                    </div>
                    <input type="hidden" id="approvePaymentId" name="payment_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Approve Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Payment Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">
                    <i class="fas fa-times-circle text-danger me-2"></i>Reject Payment
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will mark the payment as rejected. Please provide a reason.
                    </div>
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label required">Rejection Reason</label>
                        <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="3" 
                                  placeholder="Explain why this payment is being rejected..." required></textarea>
                    </div>
                    <input type="hidden" id="rejectPaymentId" name="payment_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>Reject Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}
.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.875rem;
}

.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.avatar {
    font-weight: 600;
}

.font-monospace {
    font-family: 'Courier New', monospace;
}

.list-group-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.list-group-item:last-child {
    border-bottom: none;
}

#proofImage {
    max-height: 500px;
    object-fit: contain;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
@endpush

@push('scripts')
<script>
function viewProof(paymentId) {
    // Fetch payment details including screenshot
    fetch(`/admin/payments/${paymentId}/proof`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('proofImage').src = data.screenshot_url;
                document.getElementById('proofDetails').innerHTML = `
                    <div class="row text-start">
                        <div class="col-md-6">
                            <strong>Order ID:</strong> #${data.order_id}<br>
                            <strong>Amount:</strong> ₹${data.amount}<br>
                            <strong>Customer:</strong> ${data.customer_name}
                        </div>
                        <div class="col-md-6">
                            <strong>Transaction ID:</strong> ${data.transaction_id}<br>
                            <strong>Submitted:</strong> ${data.submitted_at}<br>
                            <strong>Payment Method:</strong> ${data.payment_method}
                        </div>
                    </div>
                    ${data.payment_note ? `<div class="mt-3"><strong>Note:</strong> ${data.payment_note}</div>` : ''}
                `;
                new bootstrap.Modal(document.getElementById('proofModal')).show();
            } else {
                showToast('Error loading payment proof', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Network error', 'error');
        });
}

function viewDetails(paymentId) {
    // Implementation for viewing detailed payment information
    showToast('Feature coming soon!', 'info');
}

function approvePayment(paymentId) {
    document.getElementById('approvePaymentId').value = paymentId;
    new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectPayment(paymentId) {
    document.getElementById('rejectPaymentId').value = paymentId;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

// Approve form submission
document.getElementById('approveForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const paymentId = formData.get('payment_id');
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    try {
        const response = await fetch(`/admin/payments/${paymentId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('approveModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(data.message || 'Error approving payment', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

// Reject form submission
document.getElementById('rejectForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const paymentId = formData.get('payment_id');
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    try {
        const response = await fetch(`/admin/payments/${paymentId}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(data.message || 'Error rejecting payment', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

function refreshPage() {
    location.reload();
}

function exportData() {
    showToast('Export feature coming soon!', 'info');
}

function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container') || document.body;
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    toastContainer.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}

// Auto refresh every 30 seconds for pending payments
setInterval(() => {
    const pendingCount = {{ $stats['pending_count'] }};
    if (pendingCount > 0) {
        // Only auto-refresh if there are pending payments
        location.reload();
    }
}, 30000);
</script>
@endpush 
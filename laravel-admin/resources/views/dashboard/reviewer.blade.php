@extends('layouts.app')

@section('title', 'Reviewer Dashboard')

@section('content')
<div class="page-content">
    <div class="container">
        <!-- Welcome Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h1 class="h3 mb-1">Welcome back, {{ auth()->user()->name }}!</h1>
                        <p class="text-muted mb-0">Ready to earn from authentic reviews?</p>
                    </div>
                    <div class="d-none d-md-block">
                        <button class="btn btn-primary" id="request-withdrawal-btn">
                            <i class="fas fa-money-check-alt me-2"></i>Request Withdrawal
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-2">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="h4 mb-1">${{ number_format($totalEarnings ?? 0, 2) }}</div>
                        <small class="text-muted">Total Earnings</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-2">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="h4 mb-1">{{ $availableReviews->count() ?? 0 }}</div>
                        <small class="text-muted">Available</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-2">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="h4 mb-1">{{ $claimedReviews->count() ?? 0 }}</div>
                        <small class="text-muted">Claimed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-2">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="h4 mb-1">80%</div>
                        <small class="text-muted">Your Share</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Withdrawal Button -->
        <div class="d-md-none mb-4">
            <button class="btn btn-primary w-100" id="request-withdrawal-btn-mobile">
                <i class="fas fa-money-check-alt me-2"></i>Request Withdrawal
            </button>
        </div>

        <div class="row g-4">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Available Reviews -->
                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">
                            <i class="fas fa-search text-primary me-2"></i>Available Reviews
                        </h5>
                        <button class="btn btn-outline-primary btn-sm" id="refresh-available-btn">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body" id="available-reviews-container">
                        @forelse($availableReviews ?? [] as $review)
                        <div class="card border-0 bg-light mb-3 review-card" data-id="{{ $review->id }}">
                            <div class="card-body">
                                <div class="d-flex align-items-start justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="platform-icon me-3">
                                            @if($review->platform === 'Google')
                                                <i class="fab fa-google fa-2x text-danger"></i>
                                            @elseif($review->platform === 'Trustpilot')
                                                <i class="fas fa-star fa-2x text-warning"></i>
                                            @elseif($review->platform === 'Yelp')
                                                <i class="fab fa-yelp fa-2x text-danger"></i>
                                            @elseif($review->platform === 'Facebook')
                                                <i class="fab fa-facebook fa-2x text-primary"></i>
                                            @else
                                                <i class="fas fa-globe fa-2x text-secondary"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <h6 class="mb-1">{{ $review->business_name }}</h6>
                                            <small class="text-muted">{{ $review->platform }} Review</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="h5 text-success mb-1">${{ number_format($review->amount ?? 0, 2) }}</div>
                                        <small class="text-muted">You earn: ${{ number_format(($review->amount ?? 0) * 0.8, 2) }}</small>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label small fw-semibold">Review Text:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-sm review-text" 
                                               value="{{ $review->review_text ?? '' }}" readonly>
                                        <button class="btn btn-outline-secondary btn-sm copy-btn" 
                                                data-text="{{ $review->review_text ?? '' }}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button class="btn btn-success flex-fill claim-review-btn" data-id="{{ $review->id }}">
                                        <i class="fas fa-hand-pointer me-1"></i>Claim Review
                                    </button>
                                    <button class="btn btn-outline-info view-details-btn" data-id="{{ $review->id }}">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No available reviews at the moment</h6>
                            <p class="text-muted small mb-3">Check back later for new opportunities</p>
                            <button class="btn btn-outline-primary" id="refresh-available-btn-empty">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                        </div>
                        @endforelse
                    </div>
                </div>

                <!-- My Claimed Reviews -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check text-warning me-2"></i>My Claimed Reviews
                        </h5>
                        <small class="text-muted">Complete these to earn your payments</small>
                    </div>
                    <div class="card-body">
                        @forelse($claimedReviews ?? [] as $review)
                        <div class="card border-warning border-2 mb-3">
                            <div class="card-body">
                                <div class="d-flex align-items-start justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="platform-icon me-3">
                                            @if($review->platform === 'Google')
                                                <i class="fab fa-google fa-2x text-danger"></i>
                                            @elseif($review->platform === 'Trustpilot')
                                                <i class="fas fa-star fa-2x text-warning"></i>
                                            @elseif($review->platform === 'Yelp')
                                                <i class="fab fa-yelp fa-2x text-danger"></i>
                                            @elseif($review->platform === 'Facebook')
                                                <i class="fab fa-facebook fa-2x text-primary"></i>
                                            @else
                                                <i class="fas fa-globe fa-2x text-secondary"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <h6 class="mb-1">{{ $review->business_name }}</h6>
                                            <small class="text-muted">{{ $review->platform }} Review</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-warning text-dark mb-1">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                        <div class="h6 text-success mb-0">Earn: ${{ number_format(($review->amount ?? 0) * 0.8, 2) }}</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label small fw-semibold">Review Text:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control form-control-sm review-text" 
                                               value="{{ $review->review_text ?? '' }}" readonly>
                                        <button class="btn btn-outline-secondary btn-sm copy-btn" 
                                                data-text="{{ $review->review_text ?? '' }}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <a href="{{ $review->business_url ?? '#' }}" target="_blank" 
                                       class="btn btn-primary flex-fill">
                                        <i class="fas fa-external-link-alt me-1"></i>Open Review Page
                                    </a>
                                    <button class="btn btn-outline-success submit-proof-btn" data-id="{{ $review->id }}">
                                        <i class="fas fa-check me-1"></i>Submit Proof
                                    </button>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">No claimed reviews</h6>
                            <p class="text-muted small">Claim available reviews to start earning</p>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Earnings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line text-success me-2"></i>Recent Earnings
                        </h6>
                    </div>
                    <div class="card-body">
                        @if(isset($recentEarnings) && $recentEarnings->count())
                            @foreach($recentEarnings->take(5) as $earning)
                            <div class="d-flex align-items-center justify-content-between mb-2 pb-2 border-bottom">
                                <div>
                                    <div class="fw-semibold small">{{ Str::limit($earning->review->business_name ?? 'Review', 20) }}</div>
                                    <small class="text-muted">{{ $earning->created_at->format('M j, Y') }}</small>
                                </div>
                                <span class="badge bg-success">+${{ number_format($earning->amount ?? 0, 2) }}</span>
                            </div>
                            @endforeach
                        @else
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-coins fa-2x mb-2 opacity-50"></i>
                                <p class="mb-0 small">No earnings yet</p>
                                <small class="text-muted">Complete reviews to start earning</small>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Important Info -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle text-info me-2"></i>Important Guidelines
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info p-3 mb-3">
                            <div class="fw-semibold mb-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>Review Guidelines
                            </div>
                            <ul class="mb-0 small">
                                <li>Write authentic, helpful reviews</li>
                                <li>Follow platform guidelines</li>
                                <li>Use your genuine account</li>
                                <li>Complete within 24 hours</li>
                            </ul>
                        </div>
                        
                        <div class="row g-2 text-center">
                            <div class="col-6">
                                <div class="bg-light p-2 rounded">
                                    <div class="fw-bold text-success">80%</div>
                                    <small class="text-muted">Your Share</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="bg-light p-2 rounded">
                                    <div class="fw-bold text-primary">10</div>
                                    <small class="text-muted">Daily Limit</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt text-warning me-2"></i>Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" id="view-wallet-btn">
                                <i class="fas fa-wallet me-2"></i>View Wallet
                            </button>
                            <button class="btn btn-outline-success btn-sm" id="view-profile-btn">
                                <i class="fas fa-user-edit me-2"></i>Update Profile
                            </button>
                            <button class="btn btn-outline-info btn-sm" id="contact-support-btn">
                                <i class="fas fa-headset me-2"></i>Contact Support
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Details Modal -->
<div class="modal fade" id="reviewDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="review-details-content">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="claim-from-modal">Claim Review</button>
            </div>
        </div>
    </div>
</div>

<!-- Submit Proof Modal -->
<div class="modal fade" id="submitProofModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Submit Review Proof</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="proof-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Screenshot of Review *</label>
                        <input type="file" class="form-control" name="screenshot" accept="image/*" required>
                        <small class="text-muted">Upload a screenshot showing your posted review</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Review Link (Optional)</label>
                        <input type="url" class="form-control" name="review_link" placeholder="https://...">
                        <small class="text-muted">Direct link to your review if available</small>
                    </div>
                    <input type="hidden" name="review_id" id="proof-review-id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submit-proof-btn">Submit Proof</button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.review-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.review-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.platform-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--light);
    border-radius: var(--radius-md);
}

.copy-btn {
    border-left: none !important;
}

.card .bg-gradient {
    border: none;
}

@media (max-width: 768px) {
    .review-card .d-flex {
        flex-direction: column;
        align-items: stretch !important;
    }
    
    .review-card .text-end {
        text-align: left !important;
        margin-top: 0.5rem;
    }
    
    .platform-icon {
        width: 40px;
        height: 40px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy text functionality
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.getAttribute('data-text');
            navigator.clipboard.writeText(text).then(() => {
                const originalIcon = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                this.classList.remove('btn-outline-secondary');
                this.classList.add('btn-success');
                
                setTimeout(() => {
                    this.innerHTML = originalIcon;
                    this.classList.remove('btn-success');
                    this.classList.add('btn-outline-secondary');
                }, 1500);
                
                showToast('Review text copied to clipboard!', 'success');
            }).catch(() => {
                showToast('Failed to copy text', 'danger');
            });
        });
    });

    // Claim review functionality
    document.querySelectorAll('.claim-review-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const reviewId = this.getAttribute('data-id');
            const originalText = this.innerHTML;
            
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Claiming...';
            
            fetch(`/reviews/${reviewId}/claim`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Review claimed successfully!', 'success');
                    // Remove the card or move it to claimed section
                    const card = this.closest('.review-card');
                    card.style.opacity = '0';
                    card.style.transform = 'translateX(-100%)';
                    setTimeout(() => {
                        card.remove();
                        // Refresh page to update counts
                        window.location.reload();
                    }, 300);
                } else {
                    showToast(data.message || 'Failed to claim review', 'danger');
                    this.disabled = false;
                    this.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Network error. Please try again.', 'danger');
                this.disabled = false;
                this.innerHTML = originalText;
            });
        });
    });

    // View details functionality
    document.querySelectorAll('.view-details-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const reviewId = this.getAttribute('data-id');
            // Implement view details modal logic here
            showToast('Review details feature coming soon!', 'info');
        });
    });

    // Submit proof functionality
    document.querySelectorAll('.submit-proof-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const reviewId = this.getAttribute('data-id');
            document.getElementById('proof-review-id').value = reviewId;
            new bootstrap.Modal(document.getElementById('submitProofModal')).show();
        });
    });

    // Submit proof form
    document.getElementById('submit-proof-btn').addEventListener('click', function() {
        const form = document.getElementById('proof-form');
        const formData = new FormData(form);
        
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Submitting...';
        
        fetch('/reviews/submit-proof', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Proof submitted successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('submitProofModal')).hide();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showToast(data.message || 'Failed to submit proof', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Network error. Please try again.', 'danger');
        })
        .finally(() => {
            this.disabled = false;
            this.innerHTML = 'Submit Proof';
        });
    });

    // Refresh available reviews
    document.querySelectorAll('#refresh-available-btn, #refresh-available-btn-empty').forEach(btn => {
        btn.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
            
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
    });

    // Request withdrawal
    document.querySelectorAll('#request-withdrawal-btn, #request-withdrawal-btn-mobile').forEach(btn => {
        btn.addEventListener('click', function() {
            showToast('Withdrawal request feature coming soon!', 'info');
        });
    });

    // Quick actions
    document.getElementById('view-wallet-btn').addEventListener('click', function() {
        window.location.href = '/wallet';
    });

    document.getElementById('view-profile-btn').addEventListener('click', function() {
        showToast('Profile update feature coming soon!', 'info');
    });

    document.getElementById('contact-support-btn').addEventListener('click', function() {
        window.location.href = '/contact';
    });
});
</script>
@endpush
@endsection 
@extends('layouts.app')

@section('title', 'Customer Dashboard')

@push('styles')
<style>
/* Minimalist Page Layout */
.page-content {
    background: #f8fafc;
    min-height: 100vh;
    padding: 3rem 0;
}

/* Wizard Styles */
.wizard-progress {
    margin-bottom: 2rem;
}

.wizard-steps {
    position: relative;
}

.wizard-step {
    text-align: center;
    position: relative;
    z-index: 2;
}

.wizard-step .step-number {
    width: 40px;
    height: 40px;
    background: #e5e7eb;
    color: #6b7280;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.wizard-step.active .step-number {
    background: #3b82f6;
    color: white;
}

.wizard-step.completed .step-number {
    background: #10b981;
    color: white;
}

.wizard-step .step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.wizard-step.active .step-label {
    color: #3b82f6;
}

.wizard-step.completed .step-label {
    color: #10b981;
}

/* Platform Cards */
.platform-card {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    height: 100%;
}

.platform-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.platform-card.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #f8fafc);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.platform-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.platform-badge {
    background: #10b981;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.platform-content h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.platform-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.platform-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.platform-pricing {
    text-align: center;
}

.platform-pricing .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
    display: block;
}

.platform-pricing .duration {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Country Grid */
.country-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.country-item {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.country-item:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.country-item.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #f8fafc);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.country-item img {
    width: 48px;
    height: 36px;
    border-radius: 6px;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.country-item span {
    display: block;
    font-weight: 500;
    color: #1f2937;
    font-size: 0.875rem;
}

/* Review Cards */
.review-card {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    transition: all 0.3s ease;
}

.review-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.review-platform {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #1f2937;
}

.review-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.review-status.completed {
    background: #dcfce7;
    color: #166534;
}

.review-status.pending {
    background: #fef3c7;
    color: #92400e;
}

.review-status.approved {
    background: #dbeafe;
    color: #1e40af;
}

.review-details {
    margin-bottom: 1rem;
}

.review-details p {
    margin-bottom: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.review-details strong {
    color: #1f2937;
}

/* Wallet Section */
.wallet-card {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.wallet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.wallet-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.wallet-balance {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.wallet-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Wizard Steps */
.wizard-step-content {
    display: none;
}

.wizard-step-content.active {
    display: block;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Styles */
.btn-primary {
    background: #3b82f6;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-success {
    background: #10b981;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6b7280;
    color: #6b7280;
    background: transparent;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: #6b7280;
    color: white;
    transform: translateY(-2px);
}

/* Creative Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .country-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .wallet-actions {
        flex-direction: column;
    }
    
    .platform-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Toast Notifications */
.toast-container {
    z-index: 9999;
}

.toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-success {
    background: #dcfce7;
    color: #166534;
}

.toast-danger {
    background: #fee2e2;
    color: #991b1b;
}

.toast-warning {
    background: #fef3c7;
    color: #92400e;
}

.toast-info {
    background: #dbeafe;
    color: #1e40af;
}

/* Review Fields Styling */
.reviews-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.review-field {
    transition: all 0.3s ease;
}

.review-field:hover {
    transform: translateY(-2px);
}

.review-field .card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.review-field .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.review-field .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 16px;
    overflow: hidden;
}

.modal-header.bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.wallet-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.quick-amount-btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-amount-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.quick-amount-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

/* Form Control Enhancements */
.form-control-lg {
    padding: 0.875rem 1rem;
    font-size: 1rem;
    border-radius: 8px;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e5e7eb;
    background: #f8f9fa;
}

.input-group .form-control {
    border-radius: 0 8px 8px 0;
    border-left: none;
}

.input-group .form-control:focus {
    border-left: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Animation for new review fields */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.review-field.new {
    animation: slideInDown 0.3s ease-out;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .quick-amounts .d-flex {
        justify-content: center;
    }
    
    .review-field .row {
        flex-direction: column;
    }
    
    .review-field .col-md-8,
    .review-field .col-md-4 {
        width: 100%;
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="page-content">
    <div class="container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h2 mb-3">Welcome back, {{ auth()->user()->name }}!</h1>
                <p class="text-muted">Manage your review requests and track your progress</p>
            </div>
        </div>

        <!-- Wallet Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="wallet-card">
                    <div class="wallet-header">
                        <h3 class="wallet-title">Wallet Balance</h3>
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                    <h2 class="wallet-balance balance-amount">₹{{ number_format(auth()->user()->wallet_balance ?? 0, 2) }}</h2>
                    <div class="wallet-actions">
                        <button class="btn btn-light" onclick="showAddMoneyModal()">
                            <i class="fas fa-plus me-2"></i>Add Money
                        </button>
                        <a href="{{ route('wallet.index') }}" class="btn btn-outline-light">
                            <i class="fas fa-history me-2"></i>Transaction History
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-grid">
                    <div class="stat-card fade-in-up">
                        <div class="stat-number">{{ $stats['total_reviews'] ?? 0 }}</div>
                        <div class="stat-label">Total Reviews</div>
                    </div>
                    <div class="stat-card fade-in-up">
                        <div class="stat-number">{{ $stats['completed_reviews'] ?? 0 }}</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card fade-in-up">
                        <div class="stat-number">{{ $stats['pending_reviews'] ?? 0 }}</div>
                        <div class="stat-label">Pending</div>
                    </div>
                    <div class="stat-card fade-in-up">
                        <div class="stat-number">{{ count($recentReviews) }}</div>
                        <div class="stat-label">Recent</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="row">
            <!-- Review Request Wizard -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h4 class="mb-0">
                            <i class="fas fa-magic me-2 text-primary"></i>
                            Request New Review
                        </h4>
                        <p class="text-muted mb-0">Follow the steps below to submit your review request</p>
                    </div>
                    <div class="card-body">
                        <!-- Wizard Progress -->
                        <div class="wizard-progress mb-4">
                            <div class="wizard-steps d-flex justify-content-between">
                                <div class="wizard-step active" data-step="1">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Platform</div>
                                </div>
                                <div class="wizard-step" data-step="2">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Country</div>
                                </div>
                                <div class="wizard-step" data-step="3">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Details</div>
                                </div>
                                <div class="wizard-step" data-step="4">
                                    <div class="step-number">4</div>
                                    <div class="step-label">Confirm</div>
                                </div>
                            </div>
                        </div>

                        <!-- Wizard Content -->
                        <div class="wizard-content">
                            <!-- Step 1: Platform Selection -->
                            <div class="wizard-step-content active" id="wizard-step-1">
                                <h5 class="mb-3">Choose Review Platform</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="google">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fab fa-google"></i>
                                                </div>
                                                <span class="platform-badge">Popular</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Google Reviews</h6>
                                                <div class="platform-stats">
                                                    <div class="stat">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <span>4.8/5</span>
                                                    </div>
                                                    <div class="stat">
                                                        <i class="fas fa-users text-info"></i>
                                                        <span>2.5M+</span>
                                                    </div>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">₹299</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="trustpilot">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span class="platform-badge">Trusted</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Trustpilot</h6>
                                                <div class="platform-stats">
                                                    <div class="stat">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <span>4.7/5</span>
                                                    </div>
                                                    <div class="stat">
                                                        <i class="fas fa-users text-info"></i>
                                                        <span>1.8M+</span>
                                                    </div>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">₹399</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="facebook">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fab fa-facebook"></i>
                                                </div>
                                                <span class="platform-badge">Social</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Facebook</h6>
                                                <div class="platform-stats">
                                                    <div class="stat">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <span>4.6/5</span>
                                                    </div>
                                                    <div class="stat">
                                                        <i class="fas fa-users text-info"></i>
                                                        <span>1.2M+</span>
                                                    </div>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">₹249</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="yelp">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fas fa-utensils"></i>
                                                </div>
                                                <span class="platform-badge">Local</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Yelp</h6>
                                                <div class="platform-stats">
                                                    <div class="stat">
                                                        <i class="fas fa-star text-warning"></i>
                                                        <span>4.5/5</span>
                                                    </div>
                                                    <div class="stat">
                                                        <i class="fas fa-users text-info"></i>
                                                        <span>900K+</span>
                                                    </div>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">₹199</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-3">
                                    <button class="btn btn-primary" onclick="nextStep()">
                                        Next <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Country Selection -->
                            <div class="wizard-step-content" id="wizard-step-2">
                                <h5 class="mb-3">Select Target Country</h5>
                                <div class="country-grid">
                                    <div class="country-item" data-country="india">
                                        <img src="https://flagcdn.com/in.svg" alt="India">
                                        <span>India</span>
                                    </div>
                                    <div class="country-item" data-country="usa">
                                        <img src="https://flagcdn.com/us.svg" alt="USA">
                                        <span>USA</span>
                                    </div>
                                    <div class="country-item" data-country="uk">
                                        <img src="https://flagcdn.com/gb.svg" alt="UK">
                                        <span>UK</span>
                                    </div>
                                    <div class="country-item" data-country="canada">
                                        <img src="https://flagcdn.com/ca.svg" alt="Canada">
                                        <span>Canada</span>
                                    </div>
                                    <div class="country-item" data-country="australia">
                                        <img src="https://flagcdn.com/au.svg" alt="Australia">
                                        <span>Australia</span>
                                    </div>
                                    <div class="country-item" data-country="germany">
                                        <img src="https://flagcdn.com/de.svg" alt="Germany">
                                        <span>Germany</span>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between mt-3">
                                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button class="btn btn-primary" onclick="nextStep()">
                                        Next <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Review Details -->
                            <div class="wizard-step-content" id="wizard-step-3">
                                <h5 class="mb-3">Review Details</h5>
                                <form id="reviewForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Business Name *</label>
                                                <input type="text" class="form-control" name="business_name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Business Profile URL *</label>
                                                <input type="url" class="form-control" name="business_url" placeholder="https://..." required>
                                                <small class="text-muted">Google Business, Trustpilot, Facebook, or Yelp profile URL</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Business Address *</label>
                                                <input type="text" class="form-control" name="business_address" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">Business Phone *</label>
                                                <input type="tel" class="form-control" name="business_phone" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Multiple Reviews Section -->
                                    <div class="reviews-section mt-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Review Requirements</h6>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addReviewField()">
                                                <i class="fas fa-plus me-1"></i>Add Review
                                            </button>
                                        </div>
                                        
                                        <div id="reviewsContainer">
                                            <!-- Review 1 (Default) -->
                                            <div class="review-field mb-3" data-review-id="1">
                                                <div class="card border-0 bg-light">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <span class="badge bg-primary">Review #1</span>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReviewField(1)" style="display: none;">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <div class="form-group">
                                                                    <label class="form-label">Review Text</label>
                                                                    <textarea class="form-control" name="review_text[]" rows="2" placeholder="Leave blank for our reviewers to write"></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group">
                                                                    <label class="form-label">Rating *</label>
                                                                    <select class="form-control" name="rating[]" required>
                                                                        <option value="">Select Rating</option>
                                                                        <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                                                                        <option value="4">⭐⭐⭐⭐ 4 Stars</option>
                                                                        <option value="3">⭐⭐⭐ 3 Stars</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                <div class="d-flex justify-content-between mt-3">
                                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button class="btn btn-primary" onclick="nextStep()">
                                        Next <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 4: Order Confirmation -->
                            <div class="wizard-step-content" id="wizard-step-4">
                                <h5 class="mb-3">Order Summary</h5>
                                <div class="card border-0 bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Platform</h6>
                                                <p id="summary-platform" class="text-muted">-</p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Country</h6>
                                                <p id="summary-country" class="text-muted">-</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Business</h6>
                                                <p id="summary-business" class="text-muted">-</p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Profile URL</h6>
                                                <p id="summary-url" class="text-muted">-</p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Reviews</h6>
                                                <p id="summary-rating" class="text-muted">-</p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Total Price</h6>
                                                <p id="summary-price" class="text-primary fw-bold">-</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between mt-3">
                                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button class="btn btn-success" onclick="submitOrder()">
                                        <i class="fas fa-check me-2"></i>Submit Order
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Recent Reviews -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2 text-primary"></i>
                            Recent Reviews
                        </h5>
                    </div>
                    <div class="card-body">
                        @if(count($recentReviews) > 0)
                            @foreach($recentReviews as $review)
                                <div class="review-card">
                                    <div class="review-header">
                                        <div class="review-platform">
                                            <i class="fab fa-{{ strtolower($review->platform) }} me-2"></i>
                                            {{ ucfirst($review->platform) }}
                                        </div>
                                        <span class="review-status {{ $review->status }}">
                                            {{ ucfirst($review->status) }}
                                        </span>
                                    </div>
                                    <div class="review-details">
                                        <p><strong>Business:</strong> {{ $review->business_name }}</p>
                                        <p><strong>Rating:</strong> {{ $review->rating }}/5</p>
                                        <p><strong>Date:</strong> {{ $review->created_at->format('M d, Y') }}</p>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewReviewDetails({{ $review->id }})">
                                        View Details
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>No reviews yet</p>
                                <p class="small">Start by requesting your first review!</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2 text-primary"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="showQuickStats()">
                                <i class="fas fa-chart-bar me-2"></i>View Analytics
                            </button>
                            <a href="{{ route('wallet.index') }}" class="btn btn-outline-success">
                                <i class="fas fa-wallet me-2"></i>Manage Wallet
                            </a>
                            <button class="btn btn-outline-info" onclick="showHelp()">
                                <i class="fas fa-question-circle me-2"></i>Get Help
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Money Modal -->
<div class="modal fade" id="addMoneyModal" tabindex="-1" aria-labelledby="addMoneyModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient-primary text-white border-0">
                <h5 class="modal-title" id="addMoneyModalLabel">
                    <i class="fas fa-wallet me-2"></i>Add Money to Wallet
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="wallet-icon mb-3">
                        <i class="fas fa-wallet fa-3x text-primary"></i>
                    </div>
                    <h6 class="text-muted">Current Balance: <span class="text-primary fw-bold">₹{{ number_format(auth()->user()->wallet_balance ?? 0, 2) }}</span></h6>
                </div>
                
                <form id="addMoneyForm">
                    <div class="form-group mb-3">
                        <label class="form-label fw-semibold">Amount (₹) *</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light">₹</span>
                            <input type="number" class="form-control form-control-lg" name="amount" min="100" step="100" placeholder="Enter amount" required>
                        </div>
                        <small class="text-muted">Minimum amount: ₹100</small>
                    </div>
                    
                    <div class="form-group mb-4">
                        <label class="form-label fw-semibold">Payment Method *</label>
                        <select class="form-control form-control-lg" name="payment_method" required>
                            <option value="">Select Payment Method</option>
                            <option value="upi">
                                <i class="fas fa-mobile-alt"></i> UPI Payment
                            </option>
                            <option value="card">
                                <i class="fas fa-credit-card"></i> Credit/Debit Card
                            </option>
                            <option value="netbanking">
                                <i class="fas fa-university"></i> Net Banking
                            </option>
                        </select>
                    </div>
                    
                    <!-- Quick Amount Buttons -->
                    <div class="quick-amounts mb-4">
                        <label class="form-label fw-semibold">Quick Amount</label>
                        <div class="d-flex gap-2 flex-wrap">
                            <button type="button" class="btn btn-outline-primary quick-amount-btn" data-amount="100">₹100</button>
                            <button type="button" class="btn btn-outline-primary quick-amount-btn" data-amount="500">₹500</button>
                            <button type="button" class="btn btn-outline-primary quick-amount-btn" data-amount="1000">₹1,000</button>
                            <button type="button" class="btn btn-outline-primary quick-amount-btn" data-amount="2000">₹2,000</button>
                            <button type="button" class="btn btn-outline-primary quick-amount-btn" data-amount="5000">₹5,000</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary btn-lg" onclick="proceedToPayment()">
                    <i class="fas fa-lock me-2"></i>Proceed to Payment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Review Details Modal -->
<div class="modal fade" id="reviewDetailsModal" tabindex="-1" aria-labelledby="reviewDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewDetailsModalLabel">Review Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="reviewDetailsModalBody">
                <div class="text-center text-muted py-5">Loading...</div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Global variables
let selectedPlatform = '';
let selectedCountry = '';
let orderData = {};
let reviewCounter = 1;

// Platform card click handlers
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.platform-card').forEach(card => {
        card.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            document.querySelectorAll('.platform-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            selectedPlatform = this.dataset.platform;
        });
    });

    // Country item click handlers
    document.querySelectorAll('.country-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            document.querySelectorAll('.country-item').forEach(i => i.classList.remove('selected'));
            this.classList.add('selected');
            selectedCountry = this.dataset.country;
        });
    });

    // Add money button handler
    document.querySelectorAll('[onclick*="showAddMoneyModal"]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showAddMoneyModal();
        });
    });

    // Wizard navigation handlers
    document.querySelectorAll('[onclick*="nextStep"]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            nextStep();
        });
    });
    document.querySelectorAll('[onclick*="prevStep"]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            prevStep();
        });
    });
    document.querySelectorAll('[onclick*="submitOrder"]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            submitOrder();
        });
    });

    // Quick amount button handlers
    document.querySelectorAll('.quick-amount-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const amount = this.dataset.amount;
            document.querySelector('input[name="amount"]').value = amount;
            
            // Update active state
            document.querySelectorAll('.quick-amount-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Add graceful fallback for confetti
    window.triggerConfetti = function() {
        if (!('animate' in document.createElement('div'))) return; // fallback for old browsers
        const colors = ['#667eea', '#764ba2', '#10b981', '#f59e0b'];
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = `fall ${Math.random() * 3 + 2}s linear forwards`;
            document.body.appendChild(confetti);
            setTimeout(() => {
                if (confetti.parentNode) document.body.removeChild(confetti);
            }, 5000);
        }
    };

    // Typing effect fallback
    const balanceElement = document.querySelector('.balance-amount');
    if (balanceElement) {
        const originalText = balanceElement.textContent;
        balanceElement.textContent = '';
        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                balanceElement.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 50);
            }
        };
        setTimeout(typeWriter, 1000);
    }

    // Feature detection for creative features
    if (!window.CSS || !CSS.supports('color', 'var(--primary)')) {
        // Fallback for older browsers: disable advanced effects
        document.body.classList.add('no-creative-effects');
    }

    console.log('✅ All click handlers and creative features initialized');
});

// Multiple Reviews Functions
function addReviewField() {
    reviewCounter++;
    const container = document.getElementById('reviewsContainer');
    const newReview = document.createElement('div');
    newReview.className = 'review-field mb-3 new';
    newReview.dataset.reviewId = reviewCounter;
    
    newReview.innerHTML = `
        <div class="card border-0 bg-light">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-primary">Review #${reviewCounter}</span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReviewField(${reviewCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label class="form-label">Review Text</label>
                            <textarea class="form-control" name="review_text[]" rows="2" placeholder="Leave blank for our reviewers to write"></textarea>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Rating *</label>
                            <select class="form-control" name="rating[]" required>
                                <option value="">Select Rating</option>
                                <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                                <option value="4">⭐⭐⭐⭐ 4 Stars</option>
                                <option value="3">⭐⭐⭐ 3 Stars</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.appendChild(newReview);
    
    // Remove animation class after animation completes
    setTimeout(() => {
        newReview.classList.remove('new');
    }, 300);
    
    // Update review numbers
    updateReviewNumbers();
    
    showToast(`Review #${reviewCounter} added successfully!`, 'success');
}

function removeReviewField(reviewId) {
    const reviewField = document.querySelector(`[data-review-id="${reviewId}"]`);
    if (reviewField) {
        reviewField.style.animation = 'slideInDown 0.3s ease-out reverse';
        setTimeout(() => {
            reviewField.remove();
            updateReviewNumbers();
            showToast('Review removed successfully!', 'info');
        }, 300);
    }
}

function updateReviewNumbers() {
    const reviewFields = document.querySelectorAll('.review-field');
    reviewFields.forEach((field, index) => {
        const badge = field.querySelector('.badge');
        const removeBtn = field.querySelector('.btn-outline-danger');
        
        // Update badge number
        badge.textContent = `Review #${index + 1}`;
        
        // Show/hide remove button (hide for first review)
        if (index === 0) {
            removeBtn.style.display = 'none';
        } else {
            removeBtn.style.display = 'block';
            removeBtn.onclick = () => removeReviewField(index + 1);
        }
    });
}

// Wizard Functions
function nextStep() {
    const currentStep = document.querySelector('.wizard-step-content.active');
    const currentStepNumber = parseInt(currentStep.id.replace('wizard-step-', ''));
    const nextStepNumber = currentStepNumber + 1;
    
    if (nextStepNumber <= 4) {
        // Validate current step
        if (currentStepNumber === 1 && !selectedPlatform) {
            showToast('Please select a platform', 'warning');
            return;
        }
        if (currentStepNumber === 2 && !selectedCountry) {
            showToast('Please select a country', 'warning');
            return;
        }
        if (currentStepNumber === 3) {
            const form = document.getElementById('reviewForm');
            if (!validateForm(form)) {
                showToast('Please fill in all required fields', 'warning');
                return;
            }
            // Collect form data
            const formData = new FormData(form);
            orderData = {
                platform: selectedPlatform,
                country: selectedCountry,
                business_name: formData.get('business_name'),
                business_url: formData.get('business_url'),
                business_address: formData.get('business_address'),
                business_phone: formData.get('business_phone'),
                reviews: []
            };
            
            // Collect multiple reviews
            const reviewTexts = formData.getAll('review_text[]');
            const ratings = formData.getAll('rating[]');
            
            for (let i = 0; i < reviewTexts.length; i++) {
                orderData.reviews.push({
                    review_text: reviewTexts[i] || null,
                    rating: parseInt(ratings[i])
                });
            }
            
            updateOrderSummary();
        }
        
        showStep(nextStepNumber);
    }
}

function prevStep() {
    const currentStep = document.querySelector('.wizard-step-content.active');
    const currentStepNumber = parseInt(currentStep.id.replace('wizard-step-', ''));
    const prevStepNumber = currentStepNumber - 1;
    
    if (prevStepNumber >= 1) {
        showStep(prevStepNumber);
    }
}

function showStep(stepNumber) {
    // Hide all steps
    document.querySelectorAll('.wizard-step-content').forEach(step => {
        step.classList.remove('active');
    });
    
    // Show target step
    document.getElementById(`wizard-step-${stepNumber}`).classList.add('active');
    
    // Update progress indicators
    document.querySelectorAll('.wizard-step').forEach((step, index) => {
        const stepNum = index + 1;
        step.classList.remove('active', 'completed');
        
        if (stepNum === stepNumber) {
            step.classList.add('active');
        } else if (stepNum < stepNumber) {
            step.classList.add('completed');
        }
    });
}

function updateOrderSummary() {
    const platformPrices = {
        'google': 299,
        'trustpilot': 399,
        'facebook': 249,
        'yelp': 199
    };
    
    const totalPrice = platformPrices[selectedPlatform] * orderData.reviews.length;
    
    document.getElementById('summary-platform').textContent = selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1);
    document.getElementById('summary-country').textContent = selectedCountry.charAt(0).toUpperCase() + selectedCountry.slice(1);
    document.getElementById('summary-business').textContent = orderData.business_name;
    document.getElementById('summary-url').textContent = orderData.business_url;
    document.getElementById('summary-rating').textContent = `${orderData.reviews.length} Review(s)`;
    document.getElementById('summary-price').textContent = '₹' + totalPrice;
}

function submitOrder() {
    // Show loading state
    const submitBtn = document.querySelector('#wizard-step-4 .btn-success');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    submitBtn.disabled = true;

    // Prepare review data as array
    const reviews = orderData.reviews.map(review => ({
        rating: review.rating,
        price: (() => {
            const platformPrices = { 'google': 299, 'trustpilot': 399, 'facebook': 249, 'yelp': 199 };
            return platformPrices[selectedPlatform];
        })(),
        platform: selectedPlatform,
        country: selectedCountry,
        business_name: orderData.business_name,
        business_url: orderData.business_url,
        business_address: orderData.business_address,
        business_phone: orderData.business_phone,
        review_text: review.review_text || null
    }));

    // Submit to backend
    fetch('/dashboard/customer/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reviews: reviews })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Review request submitted successfully!', 'success');
            if (typeof triggerConfetti === 'function') {
                triggerConfetti();
            }
            // Reset wizard
            resetWizard();
            // Reload page to update stats
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showToast(data.message || 'Failed to submit review request', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while submitting your request', 'danger');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function resetWizard() {
    // Reset selections
    selectedPlatform = '';
    selectedCountry = '';
    orderData = {};
    reviewCounter = 1;
    
    // Reset UI
    document.querySelectorAll('.platform-card').forEach(c => c.classList.remove('selected'));
    document.querySelectorAll('.country-item').forEach(i => i.classList.remove('selected'));
    document.getElementById('reviewForm').reset();
    
    // Reset reviews container
    const container = document.getElementById('reviewsContainer');
    container.innerHTML = `
        <div class="review-field mb-3" data-review-id="1">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">Review #1</span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReviewField(1)" style="display: none;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">Review Text</label>
                                <textarea class="form-control" name="review_text[]" rows="2" placeholder="Leave blank for our reviewers to write"></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Rating *</label>
                                <select class="form-control" name="rating[]" required>
                                    <option value="">Select Rating</option>
                                    <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                                    <option value="4">⭐⭐⭐⭐ 4 Stars</option>
                                    <option value="3">⭐⭐⭐ 3 Stars</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Go back to step 1
    showStep(1);
}

// Modal Functions
function showAddMoneyModal() {
    const modal = new bootstrap.Modal(document.getElementById('addMoneyModal'));
    modal.show();
    
    // Reset quick amount buttons
    document.querySelectorAll('.quick-amount-btn').forEach(btn => btn.classList.remove('active'));
}

function proceedToPayment() {
    const form = document.getElementById('addMoneyForm');
    if (!validateForm(form)) {
        showToast('Please fill in all required fields', 'warning');
        return;
    }
    
    const formData = new FormData(form);
    const amount = formData.get('amount');
    const paymentMethod = formData.get('payment_method');
    
    // Show loading state
    const submitBtn = document.querySelector('#addMoneyModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Payment...';
    submitBtn.disabled = true;
    
    // Simulate payment processing
    setTimeout(() => {
        showToast('Payment processed successfully!', 'success');
        if (typeof triggerConfetti === 'function') {
            triggerConfetti();
        }
        
        // Close modal and reload page
        bootstrap.Modal.getInstance(document.getElementById('addMoneyModal')).hide();
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Review Details Functions
function viewReviewDetails(reviewId) {
    const modal = new bootstrap.Modal(document.getElementById('reviewDetailsModal'));
    modal.show();
    
    // Load review details via AJAX
    fetch(`/dashboard/review-details/${reviewId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('reviewDetailsModalBody').innerHTML = data.html;
            } else {
                document.getElementById('reviewDetailsModalBody').innerHTML = 
                    '<div class="text-center text-muted py-5">Failed to load review details</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('reviewDetailsModalBody').innerHTML = 
                '<div class="text-center text-muted py-5">Error loading review details</div>';
        });
}

// Utility Functions
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        }
    });
    
    return isValid;
}

function showQuickStats() {
    const stats = {
        totalReviews: {{ $stats['total_reviews'] ?? 0 }},
        completedReviews: {{ $stats['completed_reviews'] ?? 0 }},
        pendingReviews: {{ $stats['pending_reviews'] ?? 0 }},
        walletBalance: {{ auth()->user()->wallet_balance ?? 0 }}
    };
    
    const message = `
📊 Quick Analytics:
• Total Reviews: ${stats.totalReviews}
• Completed: ${stats.completedReviews}
• Pending: ${stats.pendingReviews}
• Wallet Balance: ₹${stats.walletBalance.toFixed(2)}
    `;
    
    showToast('Analytics loaded! Check console for details', 'info');
    console.log(message);
}

function showHelp() {
    const helpContent = `
🎯 How to use this dashboard:

1. **Add Money**: Click "Add Money" to fund your wallet
2. **Select Platform**: Choose from Google, Trustpilot, Facebook, or Yelp
3. **Choose Country**: Select your target market
4. **Fill Details**: Enter business info and review requirements
5. **Submit Order**: Review and confirm your request

💡 Tips:
• Start with Google Reviews for best results
• Multiple reviews get better pricing
• Keep your wallet funded for quick orders
    `;
    
    showToast('Help guide opened! Check console for details', 'info');
    console.log(helpContent);
}

// Enhanced success feedback
function enhancedSuccess(message) {
    showToast(message, 'success');
    if (typeof triggerConfetti === 'function') {
        triggerConfetti();
    }
    
    // Add success sound (optional)
    if (typeof Audio !== 'undefined') {
        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWTQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        audio.play().catch(() => {}); // Ignore errors if audio fails
    }
}

// Add confetti animation for successful actions
function triggerConfetti() {
    const colors = ['#667eea', '#764ba2', '#10b981', '#f59e0b'];
    
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.position = 'fixed';
        confetti.style.left = Math.random() * 100 + 'vw';
        confetti.style.top = '-10px';
        confetti.style.width = '10px';
        confetti.style.height = '10px';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.borderRadius = '50%';
        confetti.style.pointerEvents = 'none';
        confetti.style.zIndex = '9999';
        confetti.style.animation = `fall ${Math.random() * 3 + 2}s linear forwards`;
        
        document.body.appendChild(confetti);
        
        setTimeout(() => {
            document.body.removeChild(confetti);
        }, 5000);
    }
}

// Add fall animation for confetti
const confettiStyle = document.createElement('style');
confettiStyle.textContent = `
    @keyframes fall {
        to {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(confettiStyle);

// Add hover effects for platform cards
document.addEventListener('DOMContentLoaded', function() {
    // Add tilt effect to platform cards
    document.querySelectorAll('.platform-card').forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
        });
    });
    
    // Add typing effect to balance amount
    const balanceElement = document.querySelector('.balance-amount');
    if (balanceElement) {
        const originalText = balanceElement.textContent;
        balanceElement.textContent = '';
        
        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                balanceElement.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 50);
            }
        };
        
        // Start typing effect after a delay
        setTimeout(typeWriter, 1000);
    }
    
    console.log('🎨 Creative features loaded!');
});
</script>
@endpush
@extends('layouts.app')

@section('title', 'Sign In')

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="auth-card">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <div class="auth-icon mb-3">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h1 class="auth-title">Welcome Back</h1>
                        <p class="text-muted">Choose your role and sign in securely with Google</p>
                    </div>
                    
                    <!-- Error Messages -->
                    @if ($errors->any())
                        <div class="alert alert-danger mb-4">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ $errors->first() }}
                        </div>
                    @endif
                    
                    <!-- Role Selection -->
                    <div class="role-selection mb-4">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-user-tag me-1"></i>Select Your Role
                        </label>
                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-primary w-100 role-btn" 
                                        id="customerRoleBtn" onclick="selectRole('customer')">
                                    <i class="fas fa-store d-block mb-1"></i>
                                    <small>Get Reviews</small>
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-primary w-100 role-btn" 
                                        id="reviewerRoleBtn" onclick="selectRole('reviewer')">
                                    <i class="fas fa-pen-nib d-block mb-1"></i>
                                    <small>Earn by Reviewing</small>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden role input for Google OAuth -->
                    <input type="hidden" name="role" id="selectedRole" value="customer">
                    
                    <!-- Terms -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            By continuing, you agree to our 
                            <a href="{{ route('terms') }}" class="text-decoration-none">Terms of Service</a> 
                            and 
                            <a href="{{ route('privacy') }}" class="text-decoration-none">Privacy Policy</a>
                        </small>
                    </div>
                    
                    <!-- Footer Links -->
                    <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                        <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Back to Home
                        </a>
                        <a href="{{ route('admin.login') }}" class="btn btn-outline-dark btn-sm">
                            <i class="fas fa-user-shield me-1"></i>Admin Login
                        </a>
                    </div>
                </div>
                
                <!-- Features List -->
                <div class="features-list mt-4">
                    <div class="row g-3 text-center">
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fab fa-google text-success mb-2"></i>
                                <small class="text-muted">Google Sign-In</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt text-warning mb-2"></i>
                                <small class="text-muted">Secure & Fast</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <i class="fas fa-star text-primary mb-2"></i>
                                <small class="text-muted">No Password</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Google Sign In -->
                <div class="google-signin-container">
                    <div id="g_id_onload"
                        data-client_id="{{ env('GOOGLE_CLIENT_ID', '668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com') }}"
                        data-callback="onGoogleSignIn"
                        data-auto_prompt="false"
                        data-ux_mode="popup"
                        data-context="signin"
                        data-origin="http://127.0.0.1:8080">
                    </div>
                    <div class="d-flex justify-content-center">
                        <div id="g_id_signin" 
                             class="g_id_signin" 
                             data-type="standard" 
                             data-size="large" 
                             data-theme="outline" 
                             data-text="sign_in_with" 
                             data-shape="rectangular" 
                             data-logo_alignment="left">
                        </div>
                    </div>
                    <!-- Error Display -->
                    <div id="google-error" class="alert alert-danger mt-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="google-error-message"></span>
                    </div>
                    <!-- Fallback Button -->
                    <div id="google-fallback" class="mt-3" style="display: none;">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="retryGoogleOAuth()">
                            <i class="fas fa-redo me-2"></i>Retry Google Sign-In
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content border-0">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h6 class="mb-2">Signing you in...</h6>
                <small class="text-muted">Please wait while we authenticate your account</small>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.auth-container {
    background: var(--gray-50);
    min-height: 100vh;
    position: relative;
}

.auth-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2.5rem 2rem;
    border: var(--border-width) solid var(--border-color);
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: var(--dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: var(--white);
    font-size: 2rem;
}

.auth-title {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
    color: var(--dark);
    margin-bottom: 0.5rem;
}

.role-btn {
    padding: 1rem 0.5rem;
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.role-btn i {
    font-size: 1.5rem;
}

.role-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
}

.role-btn.active {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.role-btn.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
}

.google-signin-container {
    margin: 2rem 0;
}

.features-list {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item i {
    font-size: 1.5rem;
    display: block;
}

.alert {
    border: none;
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger);
    border-left-color: var(--danger);
}

/* Google Sign-In Button Customization */
.g_id_signin {
    margin: 0 auto;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .auth-card {
        margin: 1rem;
        padding: 2rem 1.5rem;
    }
    
    .auth-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .role-btn {
        padding: 0.75rem 0.5rem;
    }
    
    .role-btn i {
        font-size: 1.25rem;
    }
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider-text {
    background: var(--white);
    padding: 0 1rem;
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
}

.login-form {
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: var(--border-width) solid var(--border-color);
}

.login-form .form-control {
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
}

.login-form .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.login-form .btn-primary {
    background: var(--primary);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.875rem 1.5rem;
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
}

.login-form .btn-primary:hover {
    background: var(--dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
</style>
@endpush

@push('scripts')
<script src="https://accounts.google.com/gsi/client" async defer></script>
<script>
let selectedRole = 'customer';

function selectRole(role) {
    selectedRole = role;
    
    // Update button states with animation
    document.querySelectorAll('.role-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const selectedBtn = document.getElementById(role + 'RoleBtn');
    selectedBtn.classList.add('active');
    
    // Update hidden input for regular login form
    document.getElementById('selectedRole').value = role;
    
    // Store in localStorage
    localStorage.setItem('selectedRole', role);
    
    // Show feedback
    showToast(`Selected: ${role === 'customer' ? 'Get Reviews' : 'Earn by Reviewing'}`, 'info');
}

function onGoogleSignIn(response) {
    console.log('Google Sign-In response received:', response);
    
    // Show loading modal
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    try {
        // Decode the JWT token
        const payload = JSON.parse(atob(response.credential.split('.')[1]));
        console.log('Decoded payload:', payload);
        
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        console.log('CSRF Token:', csrfToken);
        
        // Prepare data for Laravel
        const data = {
            email: payload.email,
            name: payload.name,
            google_id: payload.sub,
            role: selectedRole,
            avatar: payload.picture
        };
        
        console.log('Sending data to server:', data);
        
        // Send to Laravel backend
        fetch('{{ route("google.login") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                showToast('Login successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                loadingModal.hide();
                showToast(data.error || 'Login failed. Please try again.', 'danger');
            }
        })
        .catch(error => {
            loadingModal.hide();
            console.error('Fetch error:', error);
            if (error.message.includes('419')) {
                showToast('Session expired. Please refresh the page and try again.', 'warning');
            } else if (error.message.includes('403')) {
                showToast('Access denied. Please check your Google OAuth configuration.', 'danger');
            } else {
                showToast('An error occurred during login. Please try again.', 'danger');
            }
        });
    } catch (error) {
        loadingModal.hide();
        console.error('Token decode error:', error);
        showToast('Invalid response from Google. Please try again.', 'danger');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to main card
    document.querySelector('.auth-card').classList.add('animate-in');
    
    // Check URL parameter first
    const urlParams = new URLSearchParams(window.location.search);
    const urlRole = urlParams.get('role');
    
    // Check localStorage second
    const storedRole = localStorage.getItem('selectedRole');
    
    // Use URL parameter if available, otherwise use stored role, default to customer
    const roleToSelect = urlRole || storedRole || 'customer';
    selectRole(roleToSelect);
    
    // Add hover effects to feature items
    document.querySelectorAll('.feature-item').forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('animate-in');
    });
    
    // Handle role button interactions
    document.querySelectorAll('.role-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
            }
        });
        
        btn.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === '1') {
            selectRole('customer');
        } else if (e.key === '2') {
            selectRole('reviewer');
        }
    });
    
});

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 300px;
        `;
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show`;
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to container
    toastContainer.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

// Error handling for Google Sign-In
window.addEventListener('error', function(e) {
    if (e.message.includes('accounts.google.com')) {
        console.warn('Google Sign-In script failed to load');
        showGoogleError('Google Sign-In script failed to load. Please check your internet connection.');
    }
});

// Google OAuth specific error handling
window.addEventListener('message', function(event) {
    if (event.origin !== 'https://accounts.google.com') {
        return;
    }
    
    console.log('Google OAuth message received:', event.data);
    
    if (event.data.type === 'error') {
        console.error('Google OAuth error:', event.data);
        showGoogleError('Google authentication error: ' + (event.data.message || 'Unknown error'));
    }
});

// Check if Google OAuth is properly loaded
function checkGoogleOAuthStatus() {
    if (typeof google === 'undefined' || !google.accounts) {
        console.warn('Google OAuth not loaded properly');
        showGoogleError('Google OAuth is not loading properly. Please refresh the page.');
        return false;
    }
    return true;
}

// Show Google OAuth error
function showGoogleError(message) {
    const errorDiv = document.getElementById('google-error');
    const errorMessage = document.getElementById('google-error-message');
    const fallbackDiv = document.getElementById('google-fallback');
    
    errorMessage.textContent = message;
    errorDiv.style.display = 'block';
    fallbackDiv.style.display = 'block';
    
    console.error('Google OAuth Error:', message);
}

// Hide Google OAuth error
function hideGoogleError() {
    const errorDiv = document.getElementById('google-error');
    const fallbackDiv = document.getElementById('google-fallback');
    
    errorDiv.style.display = 'none';
    fallbackDiv.style.display = 'none';
}

// Retry Google OAuth
function retryGoogleOAuth() {
    hideGoogleError();
    
    // Reload the page to retry
    window.location.reload();
}

// Initialize Google OAuth with error handling
document.addEventListener('DOMContentLoaded', function() {
    // Check Google OAuth status after a short delay
    setTimeout(() => {
        if (!checkGoogleOAuthStatus()) {
            // Try to reload the Google OAuth script
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;
            script.onerror = function() {
                console.error('Failed to load Google OAuth script');
                showGoogleError('Failed to load Google authentication. Please check your internet connection and try again.');
            };
            script.onload = function() {
                console.log('Google OAuth script loaded successfully');
                hideGoogleError();
            };
            document.head.appendChild(script);
        }
    }, 2000);
    
    // Additional error checking for common Google OAuth issues
    setTimeout(() => {
        const gIdSignin = document.getElementById('g_id_signin');
        if (gIdSignin && gIdSignin.children.length === 0) {
            console.warn('Google Sign-In button not rendered');
            showGoogleError('Google Sign-In button failed to load. This might be due to origin restrictions in your Google OAuth configuration.');
        }
    }, 3000);
});
</script>
@endpush
@endsection 
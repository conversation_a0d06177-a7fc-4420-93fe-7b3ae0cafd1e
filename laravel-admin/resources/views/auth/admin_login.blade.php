@extends('layouts.app')

@section('content')
<div class="login-container">
    <div class="login-card">
        <div class="text-center mb-4">
            <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
            <div class="login-title">Admin Panel Login</div>
        </div>
        
        @if ($errors->any())
            <div class="alert alert-danger">
                {{ $errors->first() }}
            </div>
        @endif
        
        <form method="post" action="{{ route('admin.login') }}" autocomplete="off">
            @csrf
            <div class="mb-3">
                <label class="form-label">Email address</label>
                <input type="email" name="email" class="form-control" required autofocus value="{{ old('email') }}">
            </div>
            <div class="mb-3">
                <label class="form-label">Password</label>
                <input type="password" name="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>
        
        <div class="text-center mt-3">
            <a href="{{ route('admin.forgot-password') }}" class="text-decoration-none text-muted">
                <i class="fas fa-key me-1"></i>Forgot Password?
            </a>
        </div>
        
        <div class="text-center mt-3">
            <a href="{{ route('home') }}" class="text-decoration-none text-muted">
                <i class="fas fa-arrow-left me-1"></i>Back to Home
            </a>
        </div>
    </div>
</div>

<style>
body { 
    background: #f8fafc; 
    margin: 0;
    padding: 0;
}
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}
.login-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    padding: 2.5rem 2rem;
    max-width: 400px;
    width: 100%;
}
.login-title {
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2563eb;
}
.form-control {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
    font-size: 1rem;
}
.form-control:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}
.btn-primary {
    background: #2563eb;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 1rem;
}
.btn-primary:hover {
    background: #1d4ed8;
}
.alert {
    border-radius: 8px;
    border: none;
}
.alert-danger {
    background: #fef2f2;
    color: #dc2626;
    border-left: 4px solid #dc2626;
}
</style>
@endsection 
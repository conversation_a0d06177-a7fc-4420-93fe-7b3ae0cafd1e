@extends('layouts.app')

@section('title', 'Register')

@section('content')
<div class="min-vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card-modern p-4 p-md-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary mb-2">
                            <i class="fas fa-user-plus me-2"></i>Join <PERSON>Earn
                        </h2>
                        <p class="text-muted">Create your account and start earning today</p>
                    </div>

                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('register') }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label fw-semibold">Full Name</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-user text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0 @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" 
                                           placeholder="Enter your full name" required>
                                </div>
                                @error('name')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label fw-semibold">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </span>
                                    <input type="email" class="form-control border-start-0 @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" 
                                           placeholder="Enter your email" required>
                                </div>
                                @error('email')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label fw-semibold">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0 @error('password') is-invalid @enderror" 
                                           id="password" name="password" placeholder="Create a password" required>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label fw-semibold">Confirm Password</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0" 
                                           id="password_confirmation" name="password_confirmation" 
                                           placeholder="Confirm your password" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label fw-semibold">I want to:</label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-check card-modern p-3 h-100 @error('role') is-invalid @enderror" 
                                         style="cursor: pointer; border: 2px solid var(--border-color); transition: all 0.3s ease;">
                                        <input class="form-check-input" type="radio" name="role" id="role_reviewer" 
                                               value="reviewer" {{ old('role') == 'reviewer' ? 'checked' : '' }} required>
                                        <label class="form-check-label w-100" for="role_reviewer">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-star text-warning me-2"></i>
                                                <strong>Write Reviews</strong>
                                            </div>
                                            <small class="text-muted">Earn money by writing authentic reviews for businesses</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check card-modern p-3 h-100 @error('role') is-invalid @enderror" 
                                         style="cursor: pointer; border: 2px solid var(--border-color); transition: all 0.3s ease;">
                                        <input class="form-check-input" type="radio" name="role" id="role_customer" 
                                               value="customer" {{ old('role') == 'customer' ? 'checked' : '' }} required>
                                        <label class="form-check-label w-100" for="role_customer">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-store text-primary me-2"></i>
                                                <strong>Get Reviews</strong>
                                            </div>
                                            <small class="text-muted">Get authentic reviews for your business</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            @error('role')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="whatsapp" class="form-label fw-semibold">WhatsApp Number (Optional)</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fab fa-whatsapp text-success"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0 @error('whatsapp') is-invalid @enderror" 
                                           id="whatsapp" name="whatsapp" value="{{ old('whatsapp') }}" 
                                           placeholder="+91 98765 43210">
                                </div>
                                @error('whatsapp')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="upi_id" class="form-label fw-semibold">UPI ID (Optional)</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-credit-card text-info"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0 @error('upi_id') is-invalid @enderror" 
                                           id="upi_id" name="upi_id" value="{{ old('upi_id') }}" 
                                           placeholder="username@upi">
                                </div>
                                @error('upi_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary-modern btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <p class="text-muted mb-0">
                            Already have an account? 
                            <a href="{{ route('login') }}" class="text-primary fw-semibold text-decoration-none">
                                Sign in here
                            </a>
                        </p>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ route('home') }}" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.input-group-text {
    border-color: var(--border-color);
}

.form-control {
    border-color: var(--border-color);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.card-modern {
    border: none;
    box-shadow: var(--shadow-xl);
}

.form-check-input:checked + .form-check-label {
    border-color: var(--primary-color) !important;
    background-color: rgba(99, 102, 241, 0.05);
}

.form-check-input:checked ~ .card-modern {
    border-color: var(--primary-color) !important;
    background-color: rgba(99, 102, 241, 0.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle role selection styling
    const roleInputs = document.querySelectorAll('input[name="role"]');
    const roleCards = document.querySelectorAll('.form-check.card-modern');
    
    roleInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Remove active styling from all cards
            roleCards.forEach(card => {
                card.style.borderColor = 'var(--border-color)';
                card.style.backgroundColor = 'white';
            });
            
            // Add active styling to selected card
            if (this.checked) {
                const card = this.closest('.card-modern');
                card.style.borderColor = 'var(--primary-color)';
                card.style.backgroundColor = 'rgba(99, 102, 241, 0.05)';
            }
        });
    });
    
    // Initialize styling for pre-selected role
    const checkedRole = document.querySelector('input[name="role"]:checked');
    if (checkedRole) {
        const card = checkedRole.closest('.card-modern');
        card.style.borderColor = 'var(--primary-color)';
        card.style.backgroundColor = 'rgba(99, 102, 241, 0.05)';
    }
});
</script>
@endsection 
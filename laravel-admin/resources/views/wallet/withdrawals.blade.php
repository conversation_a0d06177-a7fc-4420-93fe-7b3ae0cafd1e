@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard.customer') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('reviews.create') }}">
                            <i class="fas fa-plus me-2"></i>
                            Post Review
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('reviews.my-reviews') }}">
                            <i class="fas fa-list me-2"></i>
                            My Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('wallet.index') }}">
                            <i class="fas fa-wallet me-2"></i>
                            Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('wallet.withdrawals') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Withdrawals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Withdrawals</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                    <i class="fas fa-plus me-2"></i>Request Withdrawal
                </button>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Withdrawal History -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Withdrawal History</h5>
                </div>
                <div class="card-body">
                    @if($withdrawals->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($withdrawals as $withdrawal)
                                    <tr>
                                        <td>{{ $withdrawal->created_at->format('M d, Y H:i') }}</td>
                                        <td class="fw-bold">₹{{ number_format($withdrawal->amount, 2) }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($withdrawal->payment_method) }}</span>
                                        </td>
                                        <td>
                                            @if($withdrawal->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($withdrawal->status === 'approved')
                                                <span class="badge bg-success">Approved</span>
                                            @elseif($withdrawal->status === 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($withdrawal->status) }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#detailsModal{{ $withdrawal->id }}">
                                                View Details
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-center">
                            {{ $withdrawals->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No withdrawals yet</h5>
                            <p class="text-muted">Your withdrawal history will appear here once you make withdrawal requests.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawal Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Request Withdrawal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('wallet.withdraw') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount (₹)</label>
                        <input type="number" class="form-control" id="amount" name="amount" min="10" step="0.01" required>
                        <div class="form-text">Minimum withdrawal amount: ₹10</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">Select payment method</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="paypal">PayPal</option>
                            <option value="crypto">Cryptocurrency</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_details" class="form-label">Payment Details</label>
                        <textarea class="form-control" id="payment_details" name="payment_details" rows="3" placeholder="Enter your payment details (account number, email, wallet address, etc.)" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Details Modals -->
@foreach($withdrawals as $withdrawal)
<div class="modal fade" id="detailsModal{{ $withdrawal->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Withdrawal Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Amount:</strong><br>
                        ₹{{ number_format($withdrawal->amount, 2) }}
                    </div>
                    <div class="col-6">
                        <strong>Status:</strong><br>
                        @if($withdrawal->status === 'pending')
                            <span class="badge bg-warning">Pending</span>
                        @elseif($withdrawal->status === 'approved')
                            <span class="badge bg-success">Approved</span>
                        @elseif($withdrawal->status === 'rejected')
                            <span class="badge bg-danger">Rejected</span>
                        @else
                            <span class="badge bg-secondary">{{ ucfirst($withdrawal->status) }}</span>
                        @endif
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>Payment Method:</strong><br>
                        {{ ucfirst($withdrawal->payment_method) }}
                    </div>
                    <div class="col-6">
                        <strong>Date:</strong><br>
                        {{ $withdrawal->created_at->format('M d, Y H:i') }}
                    </div>
                </div>
                <hr>
                <div>
                    <strong>Payment Details:</strong><br>
                    <p class="mt-2">{{ $withdrawal->payment_details }}</p>
                </div>
                @if($withdrawal->admin_notes)
                <hr>
                <div>
                    <strong>Admin Notes:</strong><br>
                    <p class="mt-2">{{ $withdrawal->admin_notes }}</p>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endforeach

<form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
    @csrf
</form>
@endsection 
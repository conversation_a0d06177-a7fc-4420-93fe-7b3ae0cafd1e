@extends('layouts.app')

@section('title', 'My Wallet')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-wallet text-primary me-2"></i>My Wallet
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addMoneyModal">
                        <i class="fas fa-plus me-2"></i>Add Money
                    </button>
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                        <i class="fas fa-arrow-down me-2"></i>Withdraw
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Wallet Stats -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Available Balance</h6>
                            <h2 class="mb-0">₹{{ number_format(auth()->user()->wallet_balance, 2) }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Total Earned</h6>
                            <h2 class="mb-0">₹{{ number_format($transactions->where('type', 'credit')->sum('amount'), 2) }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Pending Payments</h6>
                            <h2 class="mb-0">{{ $pendingPayments->count() }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payments -->
    @if($pendingPayments->count() > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-hourglass-half text-warning me-2"></i>Pending Payment Verification
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($pendingPayments as $payment)
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 bg-light">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">Order #{{ $payment->order_id }}</h6>
                                        <span class="badge bg-{{ $payment->status_badge }}">{{ ucfirst(str_replace('_', ' ', $payment->status)) }}</span>
                                    </div>
                                    <h5 class="text-primary mb-0">₹{{ $payment->amount }}</h5>
                                </div>
                                <small class="text-muted">
                                    Created: {{ $payment->created_at->format('M d, Y H:i') }}
                                    @if($payment->submitted_at)
                                        <br>Submitted: {{ $payment->submitted_at->format('M d, Y H:i') }}
                                    @endif
                                </small>
                                @if($payment->status === 'pending')
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-primary" onclick="resumePayment({{ $payment->id }})">
                                            <i class="fas fa-play me-1"></i>Complete Payment
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Transaction History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history text-info me-2"></i>Transaction History
                    </h5>
                </div>
                <div class="card-body">
                    @if($transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th class="text-end">Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($transactions as $transaction)
                                    <tr>
                                        <td>{{ $transaction->created_at->format('M d, Y H:i') }}</td>
                                        <td>{{ $transaction->description }}</td>
                                        <td>
                                            <span class="badge bg-{{ $transaction->type === 'credit' ? 'success' : 'danger' }}">
                                                {{ ucfirst($transaction->type) }}
                                            </span>
                                        </td>
                                        <td class="text-end">
                                            <span class="text-{{ $transaction->type === 'credit' ? 'success' : 'danger' }}">
                                                {{ $transaction->type === 'credit' ? '+' : '-' }}₹{{ number_format($transaction->amount, 2) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">Completed</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        {{ $transactions->links() }}
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No transactions yet</h6>
                            <p class="text-muted">Your transaction history will appear here.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Money Modal -->
<div class="modal fade" id="addMoneyModal" tabindex="-1" aria-labelledby="addMoneyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMoneyModalLabel">
                    <i class="fas fa-plus-circle text-success me-2"></i>Add Money to Wallet
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Step 1: Amount Selection -->
                <div id="step1" class="payment-step">
                    <h6 class="mb-3">Select Amount</h6>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="input-group input-group-lg">
                                <span class="input-group-text">₹</span>
                                <input type="number" class="form-control" id="paymentAmount" placeholder="Enter amount" min="10" max="50000">
                            </div>
                            <div class="form-text">Minimum: ₹10, Maximum: ₹50,000</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="small text-muted mb-2">Quick Select:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="setAmount(100)">₹100</button>
                                <button class="btn btn-outline-primary btn-sm" onclick="setAmount(500)">₹500</button>
                                <button class="btn btn-outline-primary btn-sm" onclick="setAmount(1000)">₹1,000</button>
                                <button class="btn btn-outline-primary btn-sm" onclick="setAmount(2000)">₹2,000</button>
                                <button class="btn btn-outline-primary btn-sm" onclick="setAmount(5000)">₹5,000</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: UPI Payment -->
                <div id="step2" class="payment-step d-none">
                    <div class="text-center mb-4">
                        <h5 class="text-success">
                            <i class="fas fa-mobile-alt me-2"></i>UPI Payment
                        </h5>
                        <p class="text-muted">Pay ₹<span id="displayAmount">0</span> using any UPI app</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white text-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-qrcode me-2"></i>Scan QR Code
                                    </h6>
                                </div>
                                <div class="card-body text-center">
                                    <div id="qrCodeContainer">
                                        <img id="qrCodeImage" src="" alt="UPI QR Code" class="img-fluid" style="max-width: 250px;">
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-success btn-sm" onclick="generateQrCode()">
                                            <i class="fas fa-refresh me-1"></i>Generate QR
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white text-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-mobile-alt me-2"></i>Pay via UPI App
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="upi-details">
                                        <div class="mb-3">
                                            <label class="form-label small text-muted">UPI ID:</label>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control" id="upiId" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="copyUpiId()">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label small text-muted">Order ID:</label>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control" id="orderId" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="copyOrderId()">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <button class="btn btn-primary w-100" onclick="openUpiApp()">
                                                <i class="fas fa-external-link-alt me-2"></i>Open UPI App
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Instructions:</strong>
                        <ol class="mb-0 mt-2">
                            <li>Scan the QR code or open UPI app using the button above</li>
                            <li>Complete the payment in your UPI app</li>
                            <li>Take a screenshot of the successful payment</li>
                            <li>Submit the payment proof below</li>
                        </ol>
                    </div>
                </div>

                <!-- Step 3: Payment Proof -->
                <div id="step3" class="payment-step d-none">
                    <div class="text-center mb-4">
                        <h5 class="text-primary">
                            <i class="fas fa-upload me-2"></i>Submit Payment Proof
                        </h5>
                        <p class="text-muted">Upload screenshot of your successful payment</p>
                    </div>

                    <form id="paymentProofForm" enctype="multipart/form-data">
                        <input type="hidden" id="paymentRequestId" name="payment_request_id">
                        
                        <div class="mb-3">
                            <label for="transactionId" class="form-label required">Transaction ID</label>
                            <input type="text" class="form-control" id="transactionId" name="transaction_id" 
                                   placeholder="Enter UPI transaction ID" required>
                            <div class="form-text">You can find this in your UPI app's transaction history</div>
                        </div>

                        <div class="mb-3">
                            <label for="paymentScreenshot" class="form-label required">Payment Screenshot</label>
                            <div class="upload-area" onclick="document.getElementById('paymentScreenshot').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-0">Click to upload screenshot</p>
                                <small class="text-muted">JPG, PNG (Max: 5MB)</small>
                            </div>
                            <input type="file" class="d-none" id="paymentScreenshot" name="payment_screenshot" 
                                   accept="image/*" required onchange="previewImage(this)">
                            <div id="imagePreview" class="mt-2"></div>
                        </div>

                        <div class="mb-3">
                            <label for="paymentNote" class="form-label">Additional Note</label>
                            <textarea class="form-control" id="paymentNote" name="payment_note" rows="3" 
                                      placeholder="Any additional information (optional)"></textarea>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                    <i class="fas fa-arrow-right me-2"></i>Continue
                </button>
                <button type="button" class="btn btn-success d-none" id="submitProofBtn" onclick="submitPaymentProof()">
                    <i class="fas fa-check me-2"></i>Submit Proof
                </button>
                <button type="button" class="btn btn-warning d-none" id="backBtn" onclick="previousStep()">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="withdrawModalLabel">
                    <i class="fas fa-arrow-down text-primary me-2"></i>Request Withdrawal
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="withdrawForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="withdrawAmount" class="form-label">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="withdrawAmount" name="amount" 
                                   min="100" max="{{ auth()->user()->wallet_balance }}" required>
                        </div>
                        <div class="form-text">
                            Available: ₹{{ number_format(auth()->user()->wallet_balance, 2) }}
                            (Minimum: ₹100)
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="upiId" class="form-label">UPI ID</label>
                        <input type="text" class="form-control" id="upiId" name="upi_id" 
                               placeholder="your-upi@paytm" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="whatsapp" class="form-label">WhatsApp Number</label>
                        <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                               placeholder="+91 98765 43210" required>
                        <div class="form-text">For withdrawal confirmation</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Submit Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.bg-gradient-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}
.bg-gradient-info {
    background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
}

.payment-step {
    transition: all 0.3s ease;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.upi-details {
    font-size: 0.9rem;
}

#imagePreview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
let currentStep = 1;
let currentPaymentData = null;

function setAmount(amount) {
    document.getElementById('paymentAmount').value = amount;
}

function nextStep() {
    if (currentStep === 1) {
        const amount = document.getElementById('paymentAmount').value;
        if (!amount || amount < 10 || amount > 50000) {
            showToast('Please enter a valid amount between ₹10 and ₹50,000', 'error');
            return;
        }
        
        // Create payment request
        createPaymentRequest(amount);
    } else if (currentStep === 2) {
        // Move to proof submission
        showStep(3);
    }
}

function previousStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.payment-step').forEach(el => el.classList.add('d-none'));
    
    // Show current step
    document.getElementById(`step${step}`).classList.remove('d-none');
    
    // Update buttons
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitProofBtn');
    const backBtn = document.getElementById('backBtn');
    
    if (step === 1) {
        nextBtn.classList.remove('d-none');
        submitBtn.classList.add('d-none');
        backBtn.classList.add('d-none');
        nextBtn.innerHTML = '<i class="fas fa-arrow-right me-2"></i>Continue';
    } else if (step === 2) {
        nextBtn.classList.remove('d-none');
        submitBtn.classList.add('d-none');
        backBtn.classList.remove('d-none');
        nextBtn.innerHTML = '<i class="fas fa-upload me-2"></i>Submit Payment Proof';
    } else if (step === 3) {
        nextBtn.classList.add('d-none');
        submitBtn.classList.remove('d-none');
        backBtn.classList.remove('d-none');
    }
    
    currentStep = step;
}

async function createPaymentRequest(amount) {
    try {
        const response = await fetch('/wallet/add-money', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                amount: amount,
                payment_method: 'upi'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            currentPaymentData = data;
            
            // Update display
            document.getElementById('displayAmount').textContent = amount;
            document.getElementById('upiId').value = data.upi_data.upi_id;
            document.getElementById('orderId').value = data.order_id;
            document.getElementById('paymentRequestId').value = data.payment_request_id;
            
            showStep(2);
        } else {
            showToast('Error creating payment request', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    }
}

async function generateQrCode() {
    if (!currentPaymentData) return;
    
    try {
        const response = await fetch('/wallet/generate-qr', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                payment_request_id: currentPaymentData.payment_request_id
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('qrCodeImage').src = data.qr_code_url;
        } else {
            showToast('Error generating QR code', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    }
}

function openUpiApp() {
    if (currentPaymentData && currentPaymentData.upi_data.intent_url) {
        window.location.href = currentPaymentData.upi_data.intent_url;
    }
}

function copyUpiId() {
    const upiId = document.getElementById('upiId');
    upiId.select();
    document.execCommand('copy');
    showToast('UPI ID copied to clipboard', 'success');
}

function copyOrderId() {
    const orderId = document.getElementById('orderId');
    orderId.select();
    document.execCommand('copy');
    showToast('Order ID copied to clipboard', 'success');
}

function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Payment Screenshot">`;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

async function submitPaymentProof() {
    const form = document.getElementById('paymentProofForm');
    const formData = new FormData(form);
    
    if (!formData.get('transaction_id') || !formData.get('payment_screenshot')) {
        showToast('Please fill in all required fields', 'error');
        return;
    }
    
    const submitBtn = document.getElementById('submitProofBtn');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
    
    try {
        const response = await fetch('/wallet/submit-proof', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addMoneyModal')).hide();
            setTimeout(() => location.reload(), 2000);
        } else {
            showToast(data.message || 'Error submitting proof', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

function resumePayment(paymentId) {
    // Implementation for resuming incomplete payments
    showToast('Feature coming soon!', 'info');
}

// Withdrawal form
document.getElementById('withdrawForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    try {
        const response = await fetch('/wallet/withdraw', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showToast(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
            setTimeout(() => location.reload(), 2000);
        } else {
            showToast(data.message, 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Network error', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

// Reset modal on close
document.getElementById('addMoneyModal').addEventListener('hidden.bs.modal', function() {
    showStep(1);
    document.getElementById('paymentAmount').value = '';
    document.getElementById('imagePreview').innerHTML = '';
    document.getElementById('paymentProofForm').reset();
    currentPaymentData = null;
});

// Auto-open modal if amount parameter is provided
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for URL parameters'); // Debug log
    
    const urlParams = new URLSearchParams(window.location.search);
    const amount = urlParams.get('amount');
    const quick = urlParams.get('quick');
    
    console.log('Amount parameter:', amount); // Debug log
    console.log('Quick parameter:', quick); // Debug log
    
    if (amount && amount > 0) {
        console.log('Setting amount and showing modal:', amount); // Debug log
        
        // Set the amount
        const amountInput = document.getElementById('paymentAmount');
        if (amountInput) {
            amountInput.value = amount;
            console.log('Amount set in input field'); // Debug log
        } else {
            console.error('paymentAmount input field not found'); // Debug log
        }
        
        // Show the modal
        const modalElement = document.getElementById('addMoneyModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal opened'); // Debug log
        } else {
            console.error('addMoneyModal not found'); // Debug log
        }
        
        // If it's a quick add, show a message
        if (quick === '1') {
            setTimeout(() => {
                console.log('Showing quick add toast'); // Debug log
                showToast(`Quick add ₹${amount} - Complete the payment below!`, 'info');
            }, 500);
        }
        
        // Clean up URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
        console.log('URL cleaned up'); // Debug log
    } else {
        console.log('No amount parameter found or amount is 0'); // Debug log
    }
});

// Auto-generate QR code when step 2 is shown
document.addEventListener('DOMContentLoaded', function() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.target.id === 'step2' && !mutation.target.classList.contains('d-none')) {
                setTimeout(generateQrCode, 500);
            }
        });
    });
    
    const step2 = document.getElementById('step2');
    if (step2) {
        observer.observe(step2, { attributes: true, attributeFilter: ['class'] });
    }
});

function showToast(message, type = 'info') {
    // Toast implementation
    const toastContainer = document.querySelector('.toast-container') || document.body;
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    toastContainer.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}
</script>
@endpush 
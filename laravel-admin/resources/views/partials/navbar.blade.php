<nav class="navbar navbar-expand-lg fixed-top transparent-navbar">
    <div class="container">
        <a class="navbar-brand" href="{{ route('home') }}">
            <strong>ReviewEarn</strong>
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                        Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}" href="{{ route('services') }}">
                        Services
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">
                        About
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">
                        Contact
                    </a>
                </li>
            </ul>

            <ul class="navbar-nav">
                @auth
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            {{ auth()->user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            @if(auth()->user()->role === 'admin')
                                <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">Admin Dashboard</a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.payments') }}">Payment Management</a></li>
                            @elseif(auth()->user()->role === 'customer')
                                <li><a class="dropdown-item" href="{{ route('dashboard.customer') }}">Customer Dashboard</a></li>
                            @elseif(auth()->user()->role === 'reviewer')
                                <li><a class="dropdown-item" href="{{ route('dashboard.reviewer') }}">Reviewer Dashboard</a></li>
                            @endif
                            <li><a class="dropdown-item" href="{{ route('wallet.index') }}">Wallet</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item">Logout</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                @else
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('login') }}">Sign In</a>
                    </li>
                @endauth
            </ul>
        </div>
    </div>
</nav>

<style>
/* Transparent navbar that overlays hero section */
.transparent-navbar {
    background-color: transparent !important;
    border-bottom: none;
    padding: 1.5rem 0;
    transition: all 0.3s ease;
}

/* When scrolled, add background */
.transparent-navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* For hero page specifically */
.transparent-navbar.on-hero {
    background-color: transparent !important;
}

.transparent-navbar.on-hero .navbar-brand {
    color: #ffffff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.transparent-navbar.on-hero .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.transparent-navbar.on-hero .nav-link:hover {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.transparent-navbar.on-hero .nav-link.active {
    color: #ffffff !important;
    font-weight: 600;
}

.transparent-navbar.on-hero .nav-link.active::after {
    background-color: #ffffff;
}

.transparent-navbar.on-hero .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Default navbar (when not on hero) */
.navbar:not(.on-hero) {
    background-color: #ffffff !important;
    border-bottom: 1px solid #e9ecef;
}

.navbar:not(.on-hero) .navbar-brand {
    color: #000000 !important;
}

.navbar:not(.on-hero) .nav-link {
    color: #6c757d !important;
}

.navbar:not(.on-hero) .nav-link:hover {
    color: #000000 !important;
    background-color: #f8f9fa;
}

.navbar:not(.on-hero) .nav-link.active {
    color: #000000 !important;
}

.navbar:not(.on-hero) .nav-link.active::after {
    background-color: #000000;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    transition: all 0.3s ease;
}

.dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    background-color: #ffffff;
}

.dropdown-item {
    color: #6c757d;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    color: #000000;
}

.dropdown-divider {
    border-color: #e9ecef;
    margin: 0.5rem 0;
}

/* Mobile navbar styles */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 8px;
    }
    
    .transparent-navbar.on-hero .navbar-collapse {
        background-color: rgba(0, 0, 0, 0.8);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .transparent-navbar.on-hero .navbar-collapse .nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
    }
    
    .transparent-navbar.on-hero .navbar-collapse .nav-link:hover {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.1);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar');
    const isHomePage = window.location.pathname === '/' || window.location.pathname.includes('home');
    
    if (isHomePage) {
        navbar.classList.add('on-hero');
        
        // Add scroll effect
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }
});
</script> 
<div class="mobile-nav d-lg-none">
    <div class="d-flex justify-content-around">
        @auth
            @if(auth()->user()->role === 'admin')
                <a href="{{ route('admin.dashboard') }}" class="mobile-nav-item {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="{{ route('admin.reviews') }}" class="mobile-nav-item {{ request()->routeIs('admin.reviews') ? 'active' : '' }}">
                    <i class="fas fa-star"></i>
                    <span>Reviews</span>
                </a>
                <a href="{{ route('admin.users') }}" class="mobile-nav-item {{ request()->routeIs('admin.users') ? 'active' : '' }}">
                    <i class="fas fa-users"></i>
                    <span>Users</span>
                </a>
            @elseif(auth()->user()->role === 'customer')
                <a href="{{ route('dashboard.customer') }}" class="mobile-nav-item {{ request()->routeIs('dashboard.customer') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="{{ route('reviews.my-reviews') }}" class="mobile-nav-item {{ request()->routeIs('reviews.my-reviews') ? 'active' : '' }}">
                    <i class="fas fa-list-alt"></i>
                    <span>My Reviews</span>
                </a>
                <a href="{{ route('wallet.index') }}" class="mobile-nav-item {{ request()->routeIs('wallet.*') ? 'active' : '' }}">
                    <i class="fas fa-wallet"></i>
                    <span>Wallet</span>
                </a>
            @elseif(auth()->user()->role === 'reviewer')
                <a href="{{ route('dashboard.reviewer') }}" class="mobile-nav-item {{ request()->routeIs('dashboard.reviewer') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="{{ route('reviews.available') }}" class="mobile-nav-item {{ request()->routeIs('reviews.available') ? 'active' : '' }}">
                    <i class="fas fa-search"></i>
                    <span>Available</span>
                </a>
                <a href="{{ route('reviews.claimed-reviews') }}" class="mobile-nav-item {{ request()->routeIs('reviews.claimed-reviews') ? 'active' : '' }}">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Claimed</span>
                </a>
                <a href="{{ route('wallet.index') }}" class="mobile-nav-item {{ request()->routeIs('wallet.*') ? 'active' : '' }}">
                    <i class="fas fa-wallet"></i>
                    <span>Wallet</span>
                </a>
            @endif
        @else
            <a href="{{ route('home') }}" class="mobile-nav-item {{ request()->routeIs('home') ? 'active' : '' }}">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="{{ route('services') }}" class="mobile-nav-item {{ request()->routeIs('services') ? 'active' : '' }}">
                <i class="fas fa-cogs"></i>
                <span>Services</span>
            </a>
            <a href="{{ route('about') }}" class="mobile-nav-item {{ request()->routeIs('about') ? 'active' : '' }}">
                <i class="fas fa-info-circle"></i>
                <span>About</span>
            </a>
            <a href="{{ route('login') }}" class="mobile-nav-item {{ request()->routeIs('login') ? 'active' : '' }}">
                <i class="fas fa-sign-in-alt"></i>
                <span>Sign In</span>
            </a>
        @endauth
    </div>
</div>

<style>
.mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-top: var(--border-width) solid var(--border-color);
    padding: var(--space-sm) 0;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--gray-600);
    font-size: 0.75rem;
    font-weight: var(--font-weight-normal);
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--border-radius);
}

.mobile-nav-item:hover {
    color: var(--dark);
    background-color: var(--gray-100);
    text-decoration: none;
}

.mobile-nav-item.active {
    color: var(--dark);
    font-weight: var(--font-weight-semibold);
}

.mobile-nav-item i {
    font-size: 1.2rem;
    margin-bottom: var(--space-xs);
    display: block;
}

.mobile-nav-item span {
    display: block;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60px;
}

/* Ensure mobile nav shows only on mobile devices */
@media (min-width: 992px) {
    .mobile-nav {
        display: none !important;
    }
}

/* Add padding to body when mobile nav is visible */
@media (max-width: 991.98px) {
    body {
        padding-bottom: 4rem;
    }
}
</style> 
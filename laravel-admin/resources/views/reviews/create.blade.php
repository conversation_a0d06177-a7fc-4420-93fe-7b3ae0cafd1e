@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Submit Review Request
                    </h4>
                </div>
                <div class="card-body">
                    <form id="reviewForm" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="platform" class="form-label">Platform *</label>
                                    <select class="form-select" id="platform" name="platform" required>
                                        <option value="">Select Platform</option>
                                        <option value="Google My Business">Google My Business</option>
                                        <option value="Facebook">Facebook</option>
                                        <option value="Instagram">Instagram</option>
                                        <option value="Twitter">Twitter</option>
                                        <option value="LinkedIn">LinkedIn</option>
                                        <option value="Yelp">Yelp</option>
                                        <option value="TripAdvisor">TripAdvisor</option>
                                        <option value="Amazon">Amazon</option>
                                        <option value="Trustpilot">Trustpilot</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount" class="form-label">Amount (₹) *</label>
                                    <input type="number" class="form-control" id="amount" name="amount" min="10" step="0.01" required>
                                    <div class="form-text">Minimum amount: ₹10</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="business_name" class="form-label">Business Name *</label>
                            <input type="text" class="form-control" id="business_name" name="business_name" required>
                        </div>

                        <div class="mb-3">
                            <label for="business_url" class="form-label">Business URL *</label>
                            <input type="url" class="form-control" id="business_url" name="business_url" placeholder="https://example.com" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="screenshot" class="form-label">Business Screenshot *</label>
                                    <input type="file" class="form-control" id="screenshot" name="screenshot" accept="image/*" required>
                                    <div class="form-text">Upload a screenshot of your business listing</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="upi_qr" class="form-label">UPI QR Code *</label>
                                    <input type="file" class="form-control" id="upi_qr" name="upi_qr" accept="image/*" required>
                                    <div class="form-text">Upload your UPI QR code for payment</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="instructions" class="form-label">Special Instructions</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="3" placeholder="Any special instructions for the reviewer..."></textarea>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('dashboard.customer') }}" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-paper-plane me-1"></i>Submit Review Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Submitting Review Request...</h5>
                <p class="text-muted mb-0">Please wait while we process your request.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('reviewForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = document.getElementById('submitBtn');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    
    // Show loading modal
    loadingModal.show();
    submitBtn.disabled = true;
    
    fetch('{{ route("reviews.store") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        submitBtn.disabled = false;
        
        if (data.success) {
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: data.message,
                confirmButtonText: 'Continue'
            }).then((result) => {
                window.location.href = '{{ route("reviews.my-reviews") }}';
            });
        } else {
            // Show error message
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: data.message,
                confirmButtonText: 'OK'
            });
        }
    })
    .catch(error => {
        loadingModal.hide();
        submitBtn.disabled = false;
        console.error('Error:', error);
        
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'An error occurred while submitting your review request. Please try again.',
            confirmButtonText: 'OK'
        });
    });
});

// Preview images
document.getElementById('screenshot').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (file.size > 2 * 1024 * 1024) { // 2MB limit
            alert('File size must be less than 2MB');
            this.value = '';
            return;
        }
    }
});

document.getElementById('upi_qr').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        if (file.size > 2 * 1024 * 1024) { // 2MB limit
            alert('File size must be less than 2MB');
            this.value = '';
            return;
        }
    }
});
</script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
.card-header {
    border-bottom: none;
}

.form-label {
    font-weight: 600;
    color: #333;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}
</style>
@endsection 
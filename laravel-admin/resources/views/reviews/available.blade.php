@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-list me-2"></i>Available Reviews</h2>
                <a href="{{ route('dashboard.reviewer') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>

            @if($reviews->count() > 0)
                <div class="row">
                    @foreach($reviews as $review)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">{{ $review->platform }}</h6>
                                    <span class="badge bg-light text-dark">₹{{ number_format($review->amount, 2) }}</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">{{ $review->business_name }}</h6>
                                <p class="card-text text-muted small">
                                    <i class="fas fa-link me-1"></i>
                                    <a href="{{ $review->business_url }}" target="_blank" class="text-decoration-none">
                                        {{ Str::limit($review->business_url, 30) }}
                                    </a>
                                </p>
                                
                                @if($review->instructions)
                                <p class="card-text small">
                                    <strong>Instructions:</strong><br>
                                    {{ Str::limit($review->instructions, 100) }}
                                </p>
                                @endif

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {{ $review->customer->name }}
                                        </small>
                                    </div>
                                    <div class="col-6 text-end">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $review->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <button class="btn btn-sm btn-outline-primary w-100" onclick="viewDetails({{ $review->id }})">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-sm btn-success w-100" onclick="claimReview({{ $review->id }})">
                                            <i class="fas fa-hand-paper me-1"></i>Claim Review
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $reviews->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Available Reviews</h4>
                    <p class="text-muted">There are currently no reviews available for claiming.</p>
                    <a href="{{ route('dashboard.reviewer') }}" class="btn btn-primary">
                        <i class="fas fa-refresh me-1"></i>Refresh
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Review Details Modal -->
<div class="modal fade" id="reviewDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="reviewDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="claimReviewBtn">
                    <i class="fas fa-hand-paper me-1"></i>Claim This Review
                </button>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
let currentReviewId = null;

function viewDetails(reviewId) {
    currentReviewId = reviewId;
    
    // Show loading
    document.getElementById('reviewDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading review details...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('reviewDetailsModal'));
    modal.show();
    
    // Load review details
    fetch(`/reviews/${reviewId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('reviewDetailsContent').innerHTML = data.html;
            } else {
                document.getElementById('reviewDetailsContent').innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <p>Error loading review details.</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('reviewDetailsContent').innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                    <p>Error loading review details.</p>
                </div>
            `;
        });
}

function claimReview(reviewId) {
    Swal.fire({
        title: 'Claim Review?',
        text: 'Are you sure you want to claim this review?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, claim it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            claimReviewAction(reviewId);
        }
    });
}

function claimReviewAction(reviewId) {
    fetch(`{{ url('/reviews') }}/${reviewId}/claim`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: data.message,
                confirmButtonText: 'Continue'
            }).then(() => {
                window.location.href = '{{ route("reviews.claimed") }}';
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: data.message,
                confirmButtonText: 'OK'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'An error occurred while claiming the review.',
            confirmButtonText: 'OK'
        });
    });
}

// Claim button in modal
document.getElementById('claimReviewBtn').addEventListener('click', function() {
    if (currentReviewId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('reviewDetailsModal'));
        modal.hide();
        claimReview(currentReviewId);
    }
});
</script>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-bottom: none;
}

.badge {
    font-size: 0.8rem;
}

.btn-sm {
    font-size: 0.8rem;
}

.modal-lg {
    max-width: 800px;
}
</style>
@endsection 
@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('dashboard.customer') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('reviews.create') }}">
                            <i class="fas fa-plus me-2"></i>
                            Post Review
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('reviews.my-reviews') }}">
                            <i class="fas fa-list me-2"></i>
                            My Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('wallet.index') }}">
                            <i class="fas fa-wallet me-2"></i>
                            Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('wallet.withdrawals') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Withdrawals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">My Reviews</h1>
                <a href="{{ route('reviews.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Post New Review
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- My Reviews -->
            <div class="row">
                @if($reviews->count() > 0)
                    @foreach($reviews as $review)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">{{ $review->business_name }}</h6>
                                    <span class="badge bg-primary">₹{{ $review->amount }}</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Platform:</strong> {{ $review->platform }}
                                </div>
                                <div class="mb-3">
                                    <strong>Business URL:</strong>
                                    <a href="{{ $review->business_url }}" target="_blank" class="text-decoration-none">
                                        {{ Str::limit($review->business_url, 30) }}
                                    </a>
                                </div>
                                <div class="mb-3">
                                    <strong>Review Text:</strong>
                                    <p class="text-muted small">{{ Str::limit($review->review_text, 100) }}</p>
                                </div>
                                <div class="mb-3">
                                    <strong>Posted:</strong> {{ $review->created_at->format('M d, Y H:i') }}
                                </div>
                                <div class="mb-3">
                                    <strong>Status:</strong>
                                    @if($review->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($review->status === 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif($review->status === 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @else
                                        <span class="badge bg-secondary">{{ ucfirst($review->status) }}</span>
                                    @endif
                                </div>
                                @if($review->claimed_by)
                                <div class="mb-3">
                                    <strong>Claimed by:</strong> 
                                    <span class="text-success">{{ $review->claimedBy->name ?? 'Unknown' }}</span>
                                </div>
                                @endif
                            </div>
                            <div class="card-footer bg-white">
                                <div class="row">
                                    <div class="col-6">
                                        <button class="btn btn-sm btn-outline-primary w-100" data-bs-toggle="modal" data-bs-target="#screenshotModal{{ $review->id }}">
                                            <i class="fas fa-image me-1"></i>Screenshot
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-sm btn-outline-info w-100" data-bs-toggle="modal" data-bs-target="#qrModal{{ $review->id }}">
                                            <i class="fas fa-qrcode me-1"></i>QR Code
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No reviews posted yet</h5>
                            <p class="text-muted">Start posting reviews to earn money and grow your business.</p>
                            <a href="{{ route('reviews.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Post Your First Review
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            @if($reviews->count() > 0)
                <div class="d-flex justify-content-center">
                    {{ $reviews->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Screenshot Modals -->
@foreach($reviews as $review)
<div class="modal fade" id="screenshotModal{{ $review->id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Review Screenshot - {{ $review->business_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ Storage::url($review->screenshot_path) }}" class="img-fluid" alt="Review Screenshot">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="qrModal{{ $review->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">UPI QR Code - {{ $review->business_name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ Storage::url($review->upi_qr_path) }}" class="img-fluid" alt="UPI QR Code">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endforeach

<form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
    @csrf
</form>
@endsection 
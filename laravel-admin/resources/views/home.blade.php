@extends('layouts.app')

@section('title', 'Home')

@section('content')
<!-- Hero Section with Background Image -->
<section class="hero-section d-flex align-items-center" id="hero">
    <!-- Video background as fallback -->
    <video autoplay loop muted playsinline class="hero-video-bg">
        <source src="{{ asset('assets/1.mp4') }}" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    
    <div class="container position-relative">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="hero-title mb-4">Our reviewers will take it from here</h1>
                <p class="hero-description mb-5">1xreviews connects businesses with real, verified reviewers to boost your online reputation on the world's most trusted platforms. Fast, secure, and transparent review services for Google, Trustpilot, Clutch, and more.</p>
                <div class="hero-actions">
                    <a href="{{ route('login') }}?role=customer" class="btn hero-btn" onclick="setRoleAndRedirect('customer')">
                        <i class="fas fa-store me-2"></i>I NEED REVIEWS
                    </a>
                    <a href="{{ route('login') }}?role=reviewer" class="btn hero-btn ms-3" onclick="setRoleAndRedirect('reviewer')">
                        <i class="fas fa-star me-2"></i>I WANT TO DO REVIEWS
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
@include('partials.services_section')

<!-- Why Choose Us Section -->
@include('partials.why_choose_us')

@endsection

@push('styles')
<link href="{{ asset('assets/styles.css') }}" rel="stylesheet">

<style>
/* Hero Section with Full Background */
.hero-section {
    min-height: 100vh;
    position: relative;
    margin: 0;
    padding: 140px 0 80px;
    overflow: hidden;
    display: flex;
    align-items: center;
}

/* Video background */
.hero-video-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Hero Text Styling */
.hero-title {
    font-size: 4rem;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 2rem;
}

.hero-description {
    font-size: 1.4rem;
    color: #ffffff;
    line-height: 1.6;
    opacity: 0.95;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Button Styling */
.hero-btn {
    background-color: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
    border-radius: 50px;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    min-width: 220px;
}

.hero-btn:hover {
    background-color: #ffffff;
    color: #000000;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.hero-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 120px 0 60px;
        min-height: 80vh;
    }
    
    .hero-title {
        font-size: 2.8rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-description {
        font-size: 1.2rem;
        margin-bottom: 2.5rem;
    }
    
    .hero-btn {
        padding: 14px 28px;
        font-size: 0.9rem;
        min-width: 200px;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-btn.ms-3 {
        margin-left: 0 !important;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 100px 0 40px;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-btn {
        width: 100%;
        max-width: 280px;
        padding: 12px 24px;
    }
}

/* Clean margins and padding throughout */
.hero-section + section {
    margin-top: 0;
    padding-top: 80px;
}

section {
    margin: 60px 0;
    padding: 40px 0;
}

.container {
    padding-left: 20px;
    padding-right: 20px;
}

@media (min-width: 768px) {
    .container {
        padding-left: 40px;
        padding-right: 40px;
    }
    
    section {
        margin: 80px 0;
        padding: 60px 0;
    }
}

/* Ensure navbar appears above hero */
.navbar {
    z-index: 1000;
}
</style>
@endpush

@push('scripts')
<script>
function setRoleAndRedirect(role) {
    // Store the selected role in localStorage for the login page
    localStorage.setItem('selectedRole', role);
    
    // The href will handle the actual navigation
    return true;
}

// Add smooth scrolling for any anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Add loading states to hero buttons
    const heroButtons = document.querySelectorAll('.hero-btn');
    
    heroButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add a subtle loading effect
            this.style.opacity = '0.8';
            
            setTimeout(() => {
                this.style.opacity = '1';
            }, 200);
        });
    });
});
</script>
@endpush 
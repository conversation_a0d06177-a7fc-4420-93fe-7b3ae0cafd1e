<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#000000">
    <title>@yield('title', 'Digital Growth Services') - ReviewEarn</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ asset('favicon.png') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            /* Minimalist Black & White Color Scheme */
            --primary: #000000;
            --secondary: #6c757d;
            --success: #495057;
            --warning: #6c757d;
            --danger: #000000;
            --info: #495057;
            --light: #f8f9fa;
            --dark: #000000;
            --white: #ffffff;
            
            /* Enhanced Grayscale Palette */
            --gray-25: #fcfcfd;
            --gray-50: #fafafa;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --gray-950: #161618;
            
            /* Advanced Typography Scale (Perfect Fourth - 1.333) */
            --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-weight-light: 300;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            /* Type Scale */
            --text-xs: 0.75rem;      /* 12px */
            --text-sm: 0.875rem;     /* 14px */
            --text-base: 1rem;       /* 16px */
            --text-lg: 1.125rem;     /* 18px */
            --text-xl: 1.25rem;      /* 20px */
            --text-2xl: 1.5rem;      /* 24px */
            --text-3xl: 1.875rem;    /* 30px */
            --text-4xl: 2.25rem;     /* 36px */
            --text-5xl: 3rem;        /* 48px */
            
            /* Line Heights */
            --leading-none: 1;
            --leading-tight: 1.25;
            --leading-snug: 1.375;
            --leading-normal: 1.5;
            --leading-relaxed: 1.625;
            --leading-loose: 2;
            
            /* Letter Spacing */
            --tracking-tighter: -0.05em;
            --tracking-tight: -0.025em;
            --tracking-normal: 0em;
            --tracking-wide: 0.025em;
            --tracking-wider: 0.05em;
            --tracking-widest: 0.1em;
            
            /* Enhanced Spacing Scale (Perfect Fourth) */
            --space-px: 1px;
            --space-0: 0rem;
            --space-1: 0.25rem;      /* 4px */
            --space-2: 0.5rem;       /* 8px */
            --space-3: 0.75rem;      /* 12px */
            --space-4: 1rem;         /* 16px */
            --space-5: 1.25rem;      /* 20px */
            --space-6: 1.5rem;       /* 24px */
            --space-8: 2rem;         /* 32px */
            --space-10: 2.5rem;      /* 40px */
            --space-12: 3rem;        /* 48px */
            --space-16: 4rem;        /* 64px */
            --space-20: 5rem;        /* 80px */
            --space-24: 6rem;        /* 96px */
            --space-32: 8rem;        /* 128px */
            
            /* Borders & Radius */
            --border-width: 1px;
            --border-width-2: 2px;
            --border-color: var(--gray-300);
            --border-color-light: var(--gray-200);
            --border-color-dark: var(--gray-400);
            --border-radius-none: 0;
            --border-radius-sm: 2px;
            --border-radius: 4px;
            --border-radius-md: 6px;
            --border-radius-lg: 8px;
            --border-radius-xl: 12px;
            --border-radius-2xl: 16px;
            --border-radius-full: 9999px;
            
            /* Enhanced Shadows */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
            
            /* Transitions */
            --transition-none: none;
            --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-default: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: 100ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* Z-Index Scale */
            --z-0: 0;
            --z-10: 10;
            --z-20: 20;
            --z-30: 30;
            --z-40: 40;
            --z-50: 50;
            --z-auto: auto;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            font-weight: var(--font-weight-normal);
            font-size: var(--text-base);
            line-height: var(--leading-normal);
            letter-spacing: var(--tracking-normal);
            color: var(--dark);
            background-color: var(--white);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: var(--font-weight-semibold);
            color: var(--dark);
            margin-top: 0;
            margin-bottom: var(--space-4);
            line-height: var(--leading-tight);
            letter-spacing: var(--tracking-tight);
        }

        h1, .h1 {
            font-size: var(--text-4xl);
            font-weight: var(--font-weight-bold);
            line-height: var(--leading-none);
            letter-spacing: var(--tracking-tighter);
        }

        h2, .h2 {
            font-size: var(--text-3xl);
            line-height: var(--leading-tight);
        }

        h3, .h3 {
            font-size: var(--text-2xl);
            line-height: var(--leading-snug);
        }

        h4, .h4 {
            font-size: var(--text-xl);
            line-height: var(--leading-snug);
        }

        h5, .h5 {
            font-size: var(--text-lg);
            line-height: var(--leading-normal);
        }

        h6, .h6 {
            font-size: var(--text-base);
            line-height: var(--leading-normal);
            letter-spacing: var(--tracking-wide);
            text-transform: uppercase;
        }

        p {
            margin-top: 0;
            margin-bottom: var(--space-4);
            line-height: var(--leading-relaxed);
        }

        .lead {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-light);
            line-height: var(--leading-relaxed);
        }

        .small {
            font-size: var(--text-sm);
            line-height: var(--leading-normal);
        }

        .text-xs { font-size: var(--text-xs); }
        .text-sm { font-size: var(--text-sm); }
        .text-base { font-size: var(--text-base); }
        .text-lg { font-size: var(--text-lg); }
        .text-xl { font-size: var(--text-xl); }

        /* Enhanced Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            font-family: inherit;
            font-weight: var(--font-weight-medium);
            font-size: var(--text-sm);
            line-height: var(--leading-none);
            letter-spacing: var(--tracking-wide);
            text-decoration: none;
            border-radius: var(--border-radius-md);
            border: var(--border-width) solid transparent;
            padding: var(--space-3) var(--space-4);
            transition: var(--transition-all);
            cursor: pointer;
            white-space: nowrap;
            min-height: 40px;
        }

        .btn:focus {
            outline: 2px solid var(--dark);
            outline-offset: 2px;
        }

        .btn-sm {
            font-size: var(--text-xs);
            padding: var(--space-2) var(--space-3);
            min-height: 32px;
        }

        .btn-lg {
            font-size: var(--text-base);
            padding: var(--space-4) var(--space-6);
            min-height: 48px;
        }

        .btn-primary {
            background-color: var(--dark);
            border-color: var(--dark);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: var(--gray-800);
            border-color: var(--gray-800);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }

        .btn-outline-primary {
            background-color: transparent;
            border-color: var(--dark);
            color: var(--dark);
        }

        .btn-outline-primary:hover {
            background-color: var(--dark);
            border-color: var(--dark);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-secondary {
            background-color: transparent;
            border-color: var(--gray-600);
            color: var(--gray-600);
        }

        .btn-outline-secondary:hover {
            background-color: var(--gray-600);
            border-color: var(--gray-600);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Enhanced Cards */
        .card {
            background-color: var(--white);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-all);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
            border-color: var(--border-color-dark);
        }

        .card-header {
            background-color: var(--gray-25);
            border-bottom: var(--border-width) solid var(--border-color);
            padding: var(--space-4) var(--space-6);
            font-weight: var(--font-weight-semibold);
            font-size: var(--text-sm);
            letter-spacing: var(--tracking-wide);
            text-transform: uppercase;
        }

        .card-body {
            padding: var(--space-6);
        }

        .card-footer {
            background-color: var(--gray-25);
            border-top: var(--border-width) solid var(--border-color);
            padding: var(--space-4) var(--space-6);
        }

        /* Enhanced Navigation */
        .navbar {
            background-color: var(--white) !important;
            border-bottom: var(--border-width) solid var(--border-color);
            box-shadow: var(--shadow-sm);
            padding: var(--space-4) 0;
            backdrop-filter: none;
        }

        .navbar-brand {
            color: var(--dark) !important;
            font-weight: var(--font-weight-bold);
            font-size: var(--text-xl);
            letter-spacing: var(--tracking-tight);
        }

        .nav-link {
            color: var(--gray-700) !important;
            font-weight: var(--font-weight-medium);
            font-size: var(--text-sm);
            letter-spacing: var(--tracking-wide);
            padding: var(--space-2) var(--space-4) !important;
            border-radius: var(--border-radius);
            transition: var(--transition-all);
            position: relative;
        }

        .nav-link:hover {
            color: var(--dark) !important;
            background-color: var(--gray-100);
        }

        .nav-link.active {
            color: var(--dark) !important;
            font-weight: var(--font-weight-semibold);
            background-color: var(--gray-100);
        }

        /* Enhanced Mobile Navigation */
        .mobile-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--white);
            border-top: var(--border-width) solid var(--border-color);
            padding: var(--space-2) 0;
            z-index: var(--z-50);
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .mobile-nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--gray-600);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-medium);
            letter-spacing: var(--tracking-wide);
            transition: var(--transition-all);
            padding: var(--space-2);
            border-radius: var(--border-radius);
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            color: var(--dark);
            background-color: var(--gray-100);
        }

        .mobile-nav-item i {
            font-size: var(--text-lg);
            margin-bottom: var(--space-1);
            display: block;
        }

        /* Enhanced Forms */
        .form-control {
            font-family: inherit;
            font-size: var(--text-sm);
            font-weight: var(--font-weight-normal);
            line-height: var(--leading-normal);
            color: var(--dark);
            background-color: var(--white);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--space-3) var(--space-4);
            transition: var(--transition-all);
            min-height: 40px;
        }

        .form-control:focus {
            border-color: var(--dark);
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
            outline: none;
        }

        .form-control:hover {
            border-color: var(--border-color-dark);
        }

        .form-label {
            color: var(--gray-700);
            font-weight: var(--font-weight-medium);
            font-size: var(--text-sm);
            letter-spacing: var(--tracking-wide);
            margin-bottom: var(--space-2);
            display: block;
        }

        /* Enhanced Alerts */
        .alert {
            padding: var(--space-4) var(--space-6);
            border-radius: var(--border-radius-lg);
            border: none;
            border-left: 4px solid;
            font-size: var(--text-sm);
            line-height: var(--leading-relaxed);
        }

        .alert-success {
            background-color: var(--gray-100);
            color: var(--gray-800);
            border-left-color: var(--gray-600);
        }

        .alert-danger {
            background-color: var(--gray-100);
            color: var(--dark);
            border-left-color: var(--dark);
        }

        .alert-warning {
            background-color: var(--gray-100);
            color: var(--gray-800);
            border-left-color: var(--gray-600);
        }

        .alert-info {
            background-color: var(--gray-100);
            color: var(--gray-800);
            border-left-color: var(--gray-600);
        }

        /* Enhanced Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            font-weight: var(--font-weight-medium);
            font-size: var(--text-xs);
            letter-spacing: var(--tracking-wide);
            text-transform: uppercase;
            border-radius: var(--border-radius-full);
            padding: var(--space-1) var(--space-3);
        }

        .badge.bg-success {
            background-color: var(--gray-600) !important;
            color: var(--white) !important;
        }

        .badge.bg-warning {
            background-color: var(--gray-500) !important;
            color: var(--white) !important;
        }

        .badge.bg-danger {
            background-color: var(--dark) !important;
            color: var(--white) !important;
        }

        .badge.bg-primary {
            background-color: var(--dark) !important;
            color: var(--white) !important;
        }

        .badge.bg-secondary {
            background-color: var(--gray-600) !important;
            color: var(--white) !important;
        }

        /* Enhanced Page Content */
        .page-content {
            min-height: 100vh;
            padding-top: var(--space-8);
            padding-bottom: var(--space-12);
        }

        /* Enhanced Utilities */
        .text-primary { color: var(--dark) !important; }
        .text-secondary { color: var(--gray-600) !important; }
        .text-success { color: var(--gray-700) !important; }
        .text-warning { color: var(--gray-600) !important; }
        .text-danger { color: var(--dark) !important; }
        .text-muted { color: var(--gray-600) !important; }

        .bg-light { background-color: var(--gray-50) !important; }
        .bg-dark { background-color: var(--dark) !important; }

        /* Enhanced Mobile Responsive */
        @media (max-width: 768px) {
            .mobile-nav {
                display: flex;
            }
            
            .page-content {
                padding-bottom: var(--space-20);
            }
            
            .d-md-none {
                display: none !important;
            }

            h1, .h1 { font-size: var(--text-3xl); }
            h2, .h2 { font-size: var(--text-2xl); }
            h3, .h3 { font-size: var(--text-xl); }
        }

        /* Remove complex animations and effects */
        * {
            -webkit-backdrop-filter: none !important;
            backdrop-filter: none !important;
        }

        /* Enhanced Loading Animation */
        .spinner-border {
            border-color: var(--gray-300);
            border-right-color: var(--dark);
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }

        /* Enhanced Toast Notifications */
        .toast {
            background-color: var(--white);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            color: var(--dark);
        }

        /* Enhanced Stat Icon Styling */
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-lg);
            background-color: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: var(--transition-all);
        }

        .stat-icon i {
            font-size: var(--text-xl);
            color: var(--gray-700);
        }

        /* Enhanced Tables */
        .table {
            margin-bottom: 0;
            font-size: var(--text-sm);
        }

        .table th {
            background-color: var(--gray-25);
            border-bottom: var(--border-width) solid var(--border-color);
            color: var(--gray-700);
            font-weight: var(--font-weight-semibold);
            font-size: var(--text-xs);
            text-transform: uppercase;
            letter-spacing: var(--tracking-wider);
            padding: var(--space-4);
        }

        .table td {
            border-bottom: var(--border-width) solid var(--gray-200);
            vertical-align: middle;
            padding: var(--space-4);
        }

        .table-hover tbody tr:hover {
            background-color: var(--gray-25);
        }
    </style>
    
    @stack('styles')
</head>

<body>
    <!-- Navigation -->
    @include('partials.navbar')

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Mobile Navigation -->
    @include('partials.mobile-nav')

    <!-- Footer -->
    @include('partials.footer')

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message"></div>
        </div>
    </div>

    <!-- jQuery (for enhanced interactions) -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Global JS -->
    <script>
        // Enhanced Toast Function with Better Animations
        function showToast(message, type = 'info', duration = 5000) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            const toastHeader = toast.querySelector('.toast-header strong');
            
            // Set message and type
            toastMessage.textContent = message;
            
            // Update header based on type
            const icons = {
                success: 'fas fa-check-circle',
                danger: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            const titles = {
                success: 'Success',
                danger: 'Error',
                warning: 'Warning',
                info: 'Information'
            };
            
            toastHeader.innerHTML = `<i class="${icons[type] || icons.info} me-2"></i>${titles[type] || titles.info}`;
            
            // Add type-specific styling
            toast.className = `toast toast-${type}`;
            
            // Show with animation
            const bsToast = new bootstrap.Toast(toast, { delay: duration });
            bsToast.show();
            
            // Add entrance animation
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            
            setTimeout(() => {
                toast.style.transition = 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)';
                toast.style.transform = 'translateX(0)';
                toast.style.opacity = '1';
            }, 10);
        }

        // Enhanced Form Validation with Better Feedback
        function validateForm(form) {
            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            let isValid = true;
            
            inputs.forEach((input, index) => {
                // Add staggered validation feedback
                setTimeout(() => {
                    if (!input.value.trim()) {
                        input.classList.add('is-invalid');
                        input.style.animation = 'shake 0.5s ease-in-out';
                        isValid = false;
                        
                        // Add focus event for better UX
                        input.addEventListener('focus', function() {
                            this.classList.remove('is-invalid');
                            this.style.animation = '';
                        }, { once: true });
                    } else {
                        input.classList.remove('is-invalid');
                        input.classList.add('is-valid');
                        input.style.animation = 'pulse-success 0.3s ease-in-out';
                    }
                }, index * 100);
            });
            
            return isValid;
        }

        // Loading States Management
        class LoadingManager {
            static show(element, text = 'Loading...') {
                if (typeof element === 'string') {
                    element = document.querySelector(element);
                }
                
                if (!element) return;
                
                // Store original content
                element.dataset.originalContent = element.innerHTML;
                element.dataset.originalDisabled = element.disabled;
                
                // Add loading state
                element.disabled = true;
                element.classList.add('loading');
                
                element.innerHTML = `
                    <span class="loading-spinner">
                        <i class="fas fa-circle-notch fa-spin me-2"></i>
                    </span>
                    <span class="loading-text">${text}</span>
                `;
                
                // Add loading animation
                element.style.transform = 'scale(0.98)';
                element.style.transition = 'all 150ms ease';
            }
            
            static hide(element) {
                if (typeof element === 'string') {
                    element = document.querySelector(element);
                }
                
                if (!element) return;
                
                // Restore original state
                element.disabled = element.dataset.originalDisabled === 'true';
                element.classList.remove('loading');
                element.innerHTML = element.dataset.originalContent;
                element.style.transform = 'scale(1)';
                
                // Add completion animation
                element.style.animation = 'pulse-success 0.3s ease-in-out';
                setTimeout(() => {
                    element.style.animation = '';
                }, 300);
            }
        }

        // Enhanced Button Interactions
        function enhanceButtons() {
            document.querySelectorAll('.btn').forEach(button => {
                // Add ripple effect on click
                button.addEventListener('click', function(e) {
                    if (this.disabled) return;
                    
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 600ms ease-out;
                        pointer-events: none;
                    `;
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => ripple.remove(), 600);
                });
                
                // Enhanced hover effects
                button.addEventListener('mouseenter', function() {
                    if (this.disabled) return;
                    this.style.transform = 'translateY(-2px)';
                });
                
                button.addEventListener('mouseleave', function() {
                    if (this.disabled) return;
                    this.style.transform = 'translateY(0)';
                });
            });
        }

        // Enhanced Card Interactions
        function enhanceCards() {
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                    this.style.boxShadow = 'var(--shadow-lg)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'var(--shadow-sm)';
                });
            });
        }

        // Smooth Page Transitions
        function addPageTransitions() {
            // Add fade-in animation to page content
            const pageContent = document.querySelector('.page-content, main');
            if (pageContent) {
                pageContent.style.opacity = '0';
                pageContent.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    pageContent.style.transition = 'all 400ms cubic-bezier(0.4, 0, 0.2, 1)';
                    pageContent.style.opacity = '1';
                    pageContent.style.transform = 'translateY(0)';
                }, 50);
            }
        }

        // Enhanced Form Field Interactions
        function enhanceFormFields() {
            document.querySelectorAll('.form-control').forEach(field => {
                // Add floating label effect
                field.addEventListener('focus', function() {
                    const label = this.previousElementSibling;
                    if (label && label.classList.contains('form-label')) {
                        label.style.transform = 'translateY(-8px) scale(0.85)';
                        label.style.color = 'var(--dark)';
                        label.style.fontWeight = 'var(--font-weight-semibold)';
                    }
                    
                    this.style.borderColor = 'var(--dark)';
                    this.style.boxShadow = '0 0 0 3px rgba(0, 0, 0, 0.1)';
                });
                
                field.addEventListener('blur', function() {
                    const label = this.previousElementSibling;
                    if (label && label.classList.contains('form-label') && !this.value) {
                        label.style.transform = 'translateY(0) scale(1)';
                        label.style.color = 'var(--gray-700)';
                        label.style.fontWeight = 'var(--font-weight-medium)';
                    }
                    
                    this.style.borderColor = 'var(--border-color)';
                    this.style.boxShadow = 'none';
                });
                
                // Add typing effect
                field.addEventListener('input', function() {
                    this.style.animation = 'subtle-pulse 0.2s ease-in-out';
                    setTimeout(() => this.style.animation = '', 200);
                });
            });
        }

        // Enhanced Table Interactions
        function enhanceTables() {
            document.querySelectorAll('.table tbody tr').forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                    this.style.backgroundColor = 'var(--gray-50)';
                    this.style.transition = 'all 150ms ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.backgroundColor = '';
                });
            });
        }

        // Progressive Loading for Dynamic Content
        function showContentWithStagger(container, delay = 100) {
            const elements = container.querySelectorAll('.card, .stat-card, .alert, .table, .btn');
            
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * delay);
            });
        }

        // Initialize all enhancements when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
            
            // Apply all enhancements
            enhanceButtons();
            enhanceCards();
            enhanceFormFields();
            enhanceTables();
            addPageTransitions();
            
            // Progressive content loading
            const mainContent = document.querySelector('.container, .container-fluid');
            if (mainContent) {
                showContentWithStagger(mainContent);
            }
            
            // Add smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Enhanced AJAX handling
        function enhancedFetch(url, options = {}) {
            const loadingElement = options.loadingElement;
            const loadingText = options.loadingText || 'Loading...';
            
            if (loadingElement) {
                LoadingManager.show(loadingElement, loadingText);
            }
            
            return fetch(url, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            })
            .then(response => {
                if (loadingElement) {
                    LoadingManager.hide(loadingElement);
                }
                return response;
            })
            .catch(error => {
                if (loadingElement) {
                    LoadingManager.hide(loadingElement);
                }
                throw error;
            });
        }

        // Make functions globally available
        window.showToast = showToast;
        window.validateForm = validateForm;
        window.LoadingManager = LoadingManager;
        window.enhancedFetch = enhancedFetch;
        window.showContentWithStagger = showContentWithStagger;
    </script>
    
    <!-- Enhanced CSS Animations -->
    <style>
        /* Micro-interaction Animations */
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }
        
        @keyframes pulse-success {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes subtle-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.01); }
            100% { transform: scale(1); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* Enhanced Loading States */
        .loading {
            pointer-events: none;
            position: relative;
            overflow: hidden;
        }
        
        .loading-spinner {
            display: inline-flex;
            align-items: center;
        }
        
        .loading-text {
            opacity: 0.8;
        }
        
        /* Enhanced Button States */
        .btn:not(:disabled):not(.disabled) {
            transform: translateY(0);
            transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .btn:not(:disabled):not(.disabled):hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .btn:not(:disabled):not(.disabled):active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        /* Enhanced Form Validation States */
        .form-control.is-invalid {
            border-color: var(--dark);
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }
        
        .form-control.is-valid {
            border-color: var(--gray-600);
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
        }
        
        /* Enhanced Card Hover States */
        .card {
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-color-dark);
        }
        
        /* Enhanced Table Row Interactions */
        .table tbody tr {
            transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .table tbody tr:hover {
            background-color: var(--gray-50);
            transform: scale(1.01);
        }
        
        /* Enhanced Mobile Nav Interactions */
        .mobile-nav-item {
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .mobile-nav-item:hover {
            transform: translateY(-2px);
            background-color: var(--gray-100);
        }
        
        .mobile-nav-item.active {
            transform: translateY(-1px);
            background-color: var(--gray-100);
            font-weight: var(--font-weight-semibold);
        }
        
        /* Enhanced Toast Styles */
        .toast {
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .toast-success {
            border-left: 4px solid var(--gray-600);
        }
        
        .toast-danger {
            border-left: 4px solid var(--dark);
        }
        
        .toast-warning {
            border-left: 4px solid var(--gray-500);
        }
        
        .toast-info {
            border-left: 4px solid var(--gray-600);
        }
        
        /* Enhanced Focus States */
        .btn:focus-visible,
        .form-control:focus-visible,
        .nav-link:focus-visible {
            outline: 2px solid var(--dark);
            outline-offset: 2px;
            box-shadow: none;
        }
        
        /* Smooth Transitions for All Interactive Elements */
        a, button, input, select, textarea, .card, .badge, .alert {
            transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Progressive Enhancement for Animations */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>

    @stack('scripts')
</body>
</html> 
# Google OAuth Origin Restrictions Fix

## Problem
The Google Sign-In button is failing to load due to origin restrictions in your Google OAuth configuration.

## Current Configuration
- **App URL**: `http://localhost:8001`
- **Google Client ID**: `668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com`

## Solution Steps

### 1. Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (the one containing the OAuth 2.0 client ID)
3. Navigate to **APIs & Services** > **Credentials**

### 2. Find Your OAuth 2.0 Client ID
1. Look for the client ID: `668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com`
2. Click on it to edit the configuration

### 3. Update Authorized Origins
In the **Authorized JavaScript origins** section, add these URLs:

```
http://localhost:8001
http://127.0.0.1:8001
```

**For Production (when you deploy):**
```
https://yourdomain.com
https://www.yourdomain.com
```

### 4. Update Authorized Redirect URIs
In the **Authorized redirect URIs** section, add:

```
http://localhost:8001/auth/google/callback
http://127.0.0.1:8001/auth/google/callback
```

**For Production:**
```
https://yourdomain.com/auth/google/callback
https://www.yourdomain.com/auth/google/callback
```

### 5. Save Changes
1. Click **Save** at the bottom of the page
2. Wait 5-10 minutes for changes to propagate

### 6. Test the Fix
1. Clear your browser cache
2. Restart your Laravel development server
3. Try the Google Sign-In button again

## Alternative: Environment Variable Configuration

You can also make the Google Client ID configurable by updating your `.env` file:

```env
# Add to your .env file
GOOGLE_CLIENT_ID=668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com
```

Then update your Blade templates to use the environment variable:

```php
data-client_id="{{ env('GOOGLE_CLIENT_ID') }}"
```

## Troubleshooting

### If the button still doesn't load:
1. Check browser console for errors
2. Verify the Google Client ID is correct
3. Ensure you're using HTTPS in production
4. Check if Google+ API is enabled in your project

### Common Error Messages:
- **"Origin not allowed"**: Add your domain to authorized origins
- **"Invalid redirect URI"**: Add your callback URL to authorized redirect URIs
- **"Client ID not found"**: Verify the client ID is correct

## Security Notes
- Never commit your `.env` file to version control
- Use different client IDs for development and production
- Regularly rotate your OAuth credentials
- Monitor your OAuth usage in Google Cloud Console

## Next Steps
After fixing the origin restrictions:
1. Test the complete OAuth flow
2. Implement proper error handling
3. Add user role selection functionality
4. Set up proper session management 
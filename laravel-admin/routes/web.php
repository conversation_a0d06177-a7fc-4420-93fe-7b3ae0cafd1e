<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', [HomeController::class, 'terms'])->name('terms');

// Authentication Routes (Google OAuth Only)
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/google-login', [AuthController::class, 'googleLogin'])->name('google.login');
Route::get('/auth/google/callback', [AuthController::class, 'handleGoogleCallback'])->name('google.callback');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Admin Routes
Route::get('/admin/login', [AuthController::class, 'showAdminLogin'])->name('admin.login');
Route::post('/admin/login', [AuthController::class, 'adminLogin'])->name('admin.login.submit');

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('dashboard');
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::get('/reviews', [AdminController::class, 'reviews'])->name('reviews');
    Route::get('/withdrawals', [AdminController::class, 'withdrawals'])->name('withdrawals');
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics');
    Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');
    Route::post('/users/{id}/status', [AdminController::class, 'updateUserStatus'])->name('users.status');
    Route::get('/export/users', [AdminController::class, 'exportUsers'])->name('export.users');
    Route::get('/export/reviews', [AdminController::class, 'exportReviews'])->name('export.reviews');
    Route::post('/payments/{id}/approve', [WalletController::class, 'approvePayment'])->name('payments.approve');
    Route::post('/payments/{id}/reject', [WalletController::class, 'rejectPayment'])->name('payments.reject');
    Route::get('/payments', [AdminController::class, 'payments'])->name('payments');
});

// Protected dashboard routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard/customer', [DashboardController::class, 'customer'])->name('dashboard.customer');
    Route::get('/dashboard/reviewer', [DashboardController::class, 'reviewer'])->name('dashboard.reviewer');
    Route::post('/dashboard/customer/submit', [DashboardController::class, 'submitMultipleReviews'])->name('dashboard.customer.submit');
    Route::get('/dashboard/review-details/{id}', [DashboardController::class, 'reviewDetails'])->name('dashboard.review.details');
});

// Review Routes
Route::middleware(['auth'])->prefix('reviews')->name('reviews.')->group(function () {
    Route::get('/', [ReviewController::class, 'index'])->name('index');
    Route::get('/create', [ReviewController::class, 'create'])->name('create');
    Route::post('/', [ReviewController::class, 'store'])->name('store');
    Route::get('/my-reviews', [ReviewController::class, 'myReviews'])->name('my-reviews');
    Route::get('/available', [ReviewController::class, 'availableReviews'])->name('available');
    Route::post('/{id}/claim', [ReviewController::class, 'claimReview'])->name('claim');
    Route::post('/{id}/submit', [ReviewController::class, 'submitReview'])->name('submit');
    Route::get('/claimed', [ReviewController::class, 'myClaimedReviews'])->name('claimed');
    Route::get('/claimed-reviews', [ReviewController::class, 'myClaimedReviews'])->name('claimed-reviews');
});

// Admin Review Management
Route::middleware(['auth', 'admin'])->prefix('admin/reviews')->name('admin.reviews.')->group(function () {
    Route::post('/{id}/approve', [ReviewController::class, 'approveReview'])->name('approve');
    Route::post('/{id}/reject', [ReviewController::class, 'rejectReview'])->name('reject');
});

// Wallet Routes
Route::middleware(['auth'])->prefix('wallet')->name('wallet.')->group(function () {
    Route::get('/', [WalletController::class, 'index'])->name('index');
    Route::post('/add-money', [WalletController::class, 'addMoney'])->name('add-money');
    Route::post('/generate-qr', [WalletController::class, 'generateUpiQr'])->name('generate-qr');
    Route::post('/submit-proof', [WalletController::class, 'submitPaymentProof'])->name('submit-proof');
    Route::post('/withdraw', [WalletController::class, 'requestWithdrawal'])->name('withdraw');
    Route::get('/withdrawals', [WalletController::class, 'myWithdrawals'])->name('withdrawals');
});

// Admin Withdrawal Management
Route::middleware(['auth', 'admin'])->prefix('admin/withdrawals')->name('admin.withdrawals.')->group(function () {
    Route::post('/{id}/approve', [WalletController::class, 'approveWithdrawal'])->name('approve');
    Route::post('/{id}/reject', [WalletController::class, 'rejectWithdrawal'])->name('reject');
});

// Test route
Route::get('/test', function () {
    return 'Laravel is working!';
});

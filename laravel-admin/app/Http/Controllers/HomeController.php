<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Review;
use App\Models\AdminSetting;

class HomeController extends Controller
{
    public function index()
    {
        // Get stats for the homepage
        $stats = [
            'total_users' => User::count(),
            'total_reviews' => Review::count(),
            'total_revenue' => Review::where('status', 'approved')->sum('amount'),
            'completion_rate' => $this->getCompletionRate(),
        ];

        // Get recent reviews for testimonials
        $recent_reviews = Review::with(['customer', 'reviewer'])
            ->where('status', 'approved')
            ->latest()
            ->limit(6)
            ->get();

        // Get top earners
        $top_earners = User::where('role', 'reviewer')
            ->orderBy('wallet_balance', 'desc')
            ->limit(5)
            ->get();

        return view('home', compact('stats', 'recent_reviews', 'top_earners'));
    }

    public function about()
    {
        return view('about');
    }

    public function services()
    {
        return view('services');
    }

    public function contact()
    {
        return view('contact');
    }

    public function privacy()
    {
        return view('privacy');
    }

    public function terms()
    {
        return view('terms');
    }

    private function getCompletionRate()
    {
        $total = Review::count();
        $completed = Review::whereIn('status', ['approved', 'rejected'])->count();
        
        return $total > 0 ? round(($completed / $total) * 100, 1) : 0;
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Review;
use App\Models\WalletTransaction;

class DashboardController extends Controller
{
    public function customer()
    {
        $user = Auth::user();
        
        if ($user->role !== 'customer') {
            return redirect('/');
        }

        // Fetch previous reviews
        $reviews = Review::where('user_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->get();
        $recentReviews = $reviews->take(5);

        // Calculate stats
        $stats = [
            'total_reviews' => $reviews->count(),
            'completed_reviews' => $reviews->where('status', 'completed')->count(),
            'pending_reviews' => $reviews->whereIn('status', ['open', 'claimed', 'submitted', 'pending', 'approved'])->count(),
        ];

        return view('dashboard.customer', compact('user', 'reviews', 'recentReviews', 'stats'));
    }

    public function submitReviewRequest(Request $request)
    {
        $user = Auth::user();
        
        if ($user->role !== 'customer') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $request->validate([
            'platform' => 'required|string',
            'country' => 'required|string',
            'business_name' => 'required|string|max:255',
            'business_url' => 'required|url',
            'review_count' => 'required|integer|min:1|max:10',
            'rating' => 'required|integer|min:3|max:5',
            'notes' => 'nullable|string'
        ]);

        // Calculate total amount
        $platformPrices = [
            'google' => 15,
            'trustpilot' => 20,
            'facebook' => 12,
            'yelp' => 18
        ];

        $pricePerReview = $platformPrices[$request->platform] ?? 15;
        $totalAmount = $pricePerReview * $request->review_count;

        // Check if user has enough balance
        if ($user->wallet_balance < $totalAmount) {
            return response()->json([
                'success' => false, 
                'message' => 'Insufficient balance. Please add more funds to your wallet.'
            ]);
        }

        try {
            DB::beginTransaction();

            // Create review request
            $review = Review::create([
                'user_id' => $user->id,
                'platform' => $request->platform,
                'country' => $request->country,
                'business_name' => $request->business_name,
                'business_url' => $request->business_url,
                'review_count' => $request->review_count,
                'rating' => $request->rating,
                'notes' => $request->notes,
                'total_amount' => $totalAmount,
                'status' => 'open'
            ]);

            // Deduct amount from wallet
            $user->decrement('wallet_balance', $totalAmount);

            // Create wallet transaction
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $totalAmount,
                'description' => "Review request for {$request->business_name} on {$request->platform}",
                'reference_id' => $review->id,
                'reference_type' => 'review'
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Review request submitted successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error submitting review request. Please try again.'
            ]);
        }
    }

    public function addBalance(Request $request)
    {
        $user = Auth::user();
        
        if ($user->role !== 'customer') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $request->validate([
            'amount' => 'required|numeric|min:10',
            'payment_method' => 'required|in:upi,bank,crypto'
        ]);

        try {
            // Create wallet transaction for balance request
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => 'credit_pending',
                'amount' => $request->amount,
                'description' => "Balance request via {$request->payment_method}",
                'payment_method' => $request->payment_method,
                'status' => 'pending'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Balance request submitted successfully! Our team will process it within 24 hours.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error submitting balance request. Please try again.'
            ]);
        }
    }

    public function reviewDetails($id)
    {
        $review = Review::where('id', $id)
                       ->where('user_id', Auth::id())
                       ->first();
        
        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found'
            ]);
        }
        
        $html = view('dashboard.review_details', compact('review'))->render();
        
        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }

    public function reviewer()
    {
        $user = Auth::user();
        
        if ($user->role !== 'reviewer') {
            return redirect('/');
        }

        // Get available reviews (not claimed)
        $availableReviews = Review::where('status', 'open')
                                 ->orderBy('created_at', 'desc')
                                 ->get();

        // Get reviewer's claimed reviews
        $claimedReviews = Review::where('claimed_by', $user->id)
                                ->orderBy('created_at', 'desc')
                                ->get();

        // Calculate total earnings
        $totalEarnings = $claimedReviews->where('status', 'completed')->sum(function($review) {
            return $review->amount * 0.8;
        });

        // Recent earnings (last 5 completed reviews)
        $recentEarnings = $claimedReviews->where('status', 'completed')->sortByDesc('completed_at')->take(5);

        return view('dashboard.reviewer', [
            'user' => $user,
            'totalEarnings' => $totalEarnings,
            'availableReviews' => $availableReviews,
            'claimedReviews' => $claimedReviews,
            'recentEarnings' => $recentEarnings,
        ]);
    }

    public function claimReview($id)
    {
        $user = Auth::user();
        
        if ($user->role !== 'reviewer') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $review = Review::where('id', $id)
                       ->where('status', 'open')
                       ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review not found or already claimed']);
        }

        try {
            $review->update([
                'reviewer_id' => $user->id,
                'status' => 'claimed',
                'claimed_at' => now()
            ]);

            return response()->json(['success' => true, 'message' => 'Review claimed successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error claiming review']);
        }
    }

    /**
     * Handle AJAX multi-review submission from customer dashboard (multiple review lines)
     */
    public function submitMultipleReviews(Request $request)
    {
        $user = Auth::user();
        if ($user->role !== 'customer') {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'business_name' => 'required|string|max:255',
            'review_url' => 'required|url',
            'description' => 'nullable|string',
            'reviews' => 'required|array|min:1',
            'reviews.*.rating' => 'required|integer|min:1|max:5',
            'reviews.*.price' => 'required|numeric|min:1|max:500',
            'reviews.*.text' => 'required|string|max:500',
        ]);

        // Get selected platform from session or request
        $platform = $request->platform ?? 'google'; // Default to google if not provided

        // Calculate total amount and validate
        $totalAmount = 0;
        $totalReviewCount = count($request->reviews); // Each line is one review
        
        foreach ($request->reviews as $reviewLine) {
            $price = floatval($reviewLine['price']);
            
            if ($price < 1) {
                return response()->json(['success' => false, 'message' => 'Price must be at least $1 for each review.']);
            }
            
            $totalAmount += $price;
        }

        // Check if user has enough balance
        if ($user->wallet_balance < $totalAmount) {
            return response()->json([
                'success' => false, 
                'message' => 'Insufficient wallet balance. Please add funds to continue.',
                'required_amount' => $totalAmount,
                'current_balance' => $user->wallet_balance
            ]);
        }

        try {
            DB::beginTransaction();
            
            $createdReviews = [];
            
            // Create one review for each review line
            foreach ($request->reviews as $lineIndex => $reviewLine) {
                $price = floatval($reviewLine['price']);
                $rating = intval($reviewLine['rating']);
                $text = $reviewLine['text'];
                
                // Create one review per line
                $review = Review::create([
                    'user_id' => $user->id,
                    'platform' => $platform,
                    'business_name' => $request->business_name,
                    'business_url' => $request->review_url,
                    'review_text' => $text,
                    'target_rating' => $rating,
                    'amount' => $price,
                    'description' => $request->description,
                    'status' => 'open',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                $createdReviews[] = $review;
                
                // Create wallet transaction for each review
                WalletTransaction::create([
                    'user_id' => $user->id,
                    'type' => 'debit',
                    'amount' => $price,
                    'description' => "Review request #{$review->id} for {$request->business_name} ({$rating} stars)",
                    'reference_id' => $review->id,
                    'reference_type' => 'review',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // Deduct total amount from user's wallet
            $user->decrement('wallet_balance', $totalAmount);
            
            DB::commit();
            
            return response()->json([
                'success' => true, 
                'message' => "Successfully created {$totalReviewCount} review requests!",
                'data' => [
                    'total_reviews' => $totalReviewCount,
                    'total_amount' => $totalAmount,
                    'review_lines' => count($request->reviews),
                    'remaining_balance' => $user->wallet_balance
                ]
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error submitting multiple reviews: ' . $e->getMessage());
            
            return response()->json([
                'success' => false, 
                'message' => 'Error submitting review requests. Please try again.',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ]);
        }
    }
} 
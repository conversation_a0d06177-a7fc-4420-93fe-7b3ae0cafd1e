<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Review;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ReviewController extends Controller
{
    public function index()
    {
        $reviews = Review::with(['customer', 'reviewer'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return view('reviews.index', compact('reviews'));
    }

    public function create()
    {
        return view('reviews.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'platform' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'business_url' => 'required|url',
            'screenshot' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'upi_qr' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'amount' => 'required|numeric|min:1',
            'instructions' => 'nullable|string',
        ]);

        try {
            // Handle screenshot upload
            $screenshotPath = $request->file('screenshot')->store('reviews/screenshots', 'public');
            
            // Handle UPI QR upload
            $upiQrPath = $request->file('upi_qr')->store('reviews/upi_qr', 'public');

            $review = Review::create([
                'user_id' => Auth::id(),
                'platform' => $request->platform,
                'business_name' => $request->business_name,
                'business_url' => $request->business_url,
                'screenshot' => $screenshotPath,
                'upi_qr' => $upiQrPath,
                'amount' => $request->amount,
                'instructions' => $request->instructions,
                'status' => 'pending',
                'unique_id' => 'REV' . strtoupper(Str::random(8)),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Review submitted successfully!',
                'review_id' => $review->id
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error submitting review: ' . $e->getMessage()
            ], 500);
        }
    }

    public function availableReviews()
    {
        $reviews = Review::where('status', 'pending')
            ->where('reviewer_id', null)
            ->with('customer')
            ->orderBy('created_at', 'asc')
            ->paginate(20);
            
        return view('reviews.available', compact('reviews'));
    }

    public function claimReview($id)
    {
        $review = Review::where('status', 'pending')
            ->where('reviewer_id', null)
            ->findOrFail($id);

        $review->update([
            'reviewer_id' => Auth::id(),
            'status' => 'claimed',
            'claimed_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review claimed successfully!'
        ]);
    }

    public function submitReview(Request $request, $id)
    {
        $request->validate([
            'review_url' => 'required|url',
            'review_screenshot' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
        ]);

        $review = Review::where('reviewer_id', Auth::id())
            ->where('status', 'claimed')
            ->findOrFail($id);

        // Handle review screenshot upload
        $reviewScreenshotPath = $request->file('review_screenshot')->store('reviews/submitted', 'public');

        $review->update([
            'review_url' => $request->review_url,
            'review_screenshot' => $reviewScreenshotPath,
            'status' => 'submitted',
            'submitted_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully!'
        ]);
    }

    public function approveReview($id)
    {
        $review = Review::findOrFail($id);
        
        $review->update([
            'status' => 'approved',
            'approved_at' => now(),
        ]);

        // Credit the reviewer
        $reviewer = $review->reviewer;
        $reviewer->increment('wallet_balance', $review->amount);

        // Create wallet transaction
        $reviewer->walletTransactions()->create([
            'type' => 'credit',
            'amount' => $review->amount,
            'description' => "Payment for review #{$review->unique_id}",
            'reference' => $review->id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review approved and payment processed!'
        ]);
    }

    public function rejectReview(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        $review = Review::findOrFail($id);
        
        $review->update([
            'status' => 'rejected',
            'rejection_reason' => $request->rejection_reason,
            'rejected_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review rejected successfully!'
        ]);
    }

    public function myReviews()
    {
        $reviews = Review::where('user_id', Auth::id())
            ->with('reviewer')
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return view('reviews.my-reviews', compact('reviews'));
    }

    public function myClaimedReviews()
    {
        $reviews = Review::where('reviewer_id', Auth::id())
            ->with('customer')
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return view('reviews.claimed-reviews', compact('reviews'));
    }
} 
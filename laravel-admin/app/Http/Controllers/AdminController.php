<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Review;
use App\Models\Withdrawal;
use App\Models\WalletTransaction;
use App\Models\AdminSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    // Removed constructor to avoid middleware issues in testing
    
    public function index()
    {
        // Dashboard statistics
        $stats = [
            'total_users' => User::count(),
            'total_customers' => User::where('role', 'customer')->count(),
            'total_reviewers' => User::where('role', 'reviewer')->count(),
            'total_reviews' => Review::count(),
            'pending_reviews' => Review::where('status', 'pending')->count(),
            'claimed_reviews' => Review::where('status', 'claimed')->count(),
            'approved_reviews' => Review::where('status', 'approved')->count(),
            'total_withdrawals' => Withdrawal::count(),
            'pending_withdrawals' => Withdrawal::where('status', 'pending')->count(),
            'total_paid' => Withdrawal::where('status', 'approved')->sum('amount'),
        ];

        // Recent activities
        $recentReviews = Review::with(['customer', 'reviewer'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentWithdrawals = Withdrawal::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentReviews', 'recentWithdrawals'));
    }

    public function users()
    {
        $users = User::withCount(['reviews', 'reviewerReviews', 'withdrawals'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users', compact('users'));
    }

    public function createUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'role' => 'required|in:customer,reviewer,admin',
            'whatsapp' => 'nullable|string',
            'upi_id' => 'nullable|string',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'whatsapp' => $request->whatsapp,
            'upi_id' => $request->upi_id,
        ]);

        Cache::forget('admin_dashboard_stats');
        
        return redirect()->back()->with('success', 'User created successfully!');
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        
        if ($user->role === 'admin') {
            return redirect()->back()->with('error', 'Cannot delete admin users.');
        }

        $user->delete();
        Cache::forget('admin_dashboard_stats');
        
        return redirect()->back()->with('success', 'User deleted successfully!');
    }

    public function updateUserStatus(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully!'
        ]);
    }

    public function resetUserPassword(Request $request, $id)
    {
        $request->validate([
            'new_password' => 'required|min:6|confirmed',
        ]);

        $user = User::findOrFail($id);
        $user->password = Hash::make($request->new_password);
        $user->save();
        
        return redirect()->back()->with('success', 'Password reset successfully!');
    }

    public function reviews()
    {
        $reviews = Review::with(['customer', 'reviewer'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.reviews', compact('reviews'));
    }

    public function approveReview($id)
    {
        DB::transaction(function () use ($id) {
            $review = Review::findOrFail($id);
            $review->status = 'approved';
            $review->save();

            // Add earnings to reviewer (80% of amount)
            $earnings = $review->amount * 0.8;
            $reviewer = $review->reviewer;
            $reviewer->wallet_balance += $earnings;
            $reviewer->save();

            // Record transaction
            WalletTransaction::create([
                'user_id' => $reviewer->id,
                'type' => 'credit',
                'amount' => $earnings,
                'review_id' => $review->id,
                'reference' => 'Review approved',
                'description' => 'Earnings from approved review'
            ]);
        });

        Cache::forget('admin_dashboard_stats');
        Cache::forget('admin_recent_activity');
        
        return redirect()->back()->with('success', 'Review approved!');
    }

    public function rejectReview(Request $request, $id)
    {
        $review = Review::findOrFail($id);
        $review->status = 'rejected';
        $review->rejection_reason = $request->rejection_reason;
        $review->reviewer_id = null; // Release back to pool
        $review->save();

        Cache::forget('admin_dashboard_stats');
        Cache::forget('admin_recent_activity');
        
        return redirect()->back()->with('success', 'Review rejected!');
    }

    public function withdrawals()
    {
        $withdrawals = Withdrawal::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.withdrawals', compact('withdrawals'));
    }

    public function approveWithdrawal($id)
    {
        $withdrawal = Withdrawal::findOrFail($id);
        $withdrawal->status = 'approved';
        $withdrawal->processed_at = now();
        $withdrawal->save();

        Cache::forget('admin_dashboard_stats');
        
        return redirect()->back()->with('success', 'Withdrawal approved!');
    }

    public function rejectWithdrawal(Request $request, $id)
    {
        DB::transaction(function () use ($request, $id) {
            $withdrawal = Withdrawal::findOrFail($id);
            $withdrawal->status = 'rejected';
            $withdrawal->rejection_reason = $request->rejection_reason;
            $withdrawal->processed_at = now();
            $withdrawal->save();

            // Refund to user wallet
            $user = $withdrawal->user;
            $user->wallet_balance += $withdrawal->amount;
            $user->save();

            // Record refund transaction
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => 'refund',
                'amount' => $withdrawal->amount,
                'reference' => 'Withdrawal rejected',
                'description' => 'Refund from rejected withdrawal'
            ]);
        });

        Cache::forget('admin_dashboard_stats');
        
        return redirect()->back()->with('success', 'Withdrawal rejected and refunded!');
    }

    public function settings()
    {
        $settings = AdminSetting::pluck('value', 'key')->toArray();
        
        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $settings = [
            'site_name' => $request->site_name,
            'site_description' => $request->site_description,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'min_withdrawal' => $request->min_withdrawal,
            'max_withdrawal' => $request->max_withdrawal,
            'review_approval_bonus' => $request->review_approval_bonus,
            'signup_bonus_customer' => $request->signup_bonus_customer,
            'signup_bonus_reviewer' => $request->signup_bonus_reviewer,
        ];

        foreach ($settings as $key => $value) {
            AdminSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully!'
        ]);
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:6|confirmed',
        ]);

        $user = Auth::user();
        
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with('error', 'Current password is incorrect.');
        }

        $user->password = Hash::make($request->new_password);
        $user->save();
        
        return redirect()->back()->with('success', 'Password changed successfully!');
    }

    public function analytics()
    {
        $totalUsers = User::count();
        $totalReviews = Review::count();
        $totalEarnings = WalletTransaction::where('type', 'credit')->sum('amount');
        
        return view('admin.analytics', compact('totalUsers', 'totalReviews', 'totalEarnings'));
    }

    public function payments()
    {
        $pendingPayments = \App\Models\PaymentRequest::where('status', 'verification_pending')
            ->with(['user'])
            ->orderBy('submitted_at', 'desc')
            ->paginate(20);

        $recentApproved = \App\Models\PaymentRequest::where('status', 'approved')
            ->with(['user', 'approvedBy'])
            ->orderBy('approved_at', 'desc')
            ->limit(10)
            ->get();

        $recentRejected = \App\Models\PaymentRequest::where('status', 'rejected')
            ->with(['user', 'rejectedBy'])
            ->orderBy('rejected_at', 'desc')
            ->limit(10)
            ->get();

        $stats = [
            'pending_count' => \App\Models\PaymentRequest::where('status', 'verification_pending')->count(),
            'approved_today' => \App\Models\PaymentRequest::where('status', 'approved')
                ->whereDate('approved_at', today())
                ->count(),
            'total_approved_amount' => \App\Models\PaymentRequest::where('status', 'approved')
                ->sum('amount'),
            'total_pending_amount' => \App\Models\PaymentRequest::where('status', 'verification_pending')
                ->sum('amount')
        ];

        return view('admin.payments', compact('pendingPayments', 'recentApproved', 'recentRejected', 'stats'));
    }

    public function exportUsers()
    {
        $users = User::all();
        
        $filename = 'users_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Name', 'Email', 'Role', 'Status', 'Wallet Balance', 'Created At']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->status,
                    $user->wallet_balance,
                    $user->created_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function exportReviews()
    {
        $reviews = Review::with(['customer', 'reviewer'])->get();
        
        $filename = 'reviews_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($reviews) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['ID', 'Unique ID', 'Customer', 'Reviewer', 'Platform', 'Business Name', 'Amount', 'Status', 'Created At']);

            foreach ($reviews as $review) {
                fputcsv($file, [
                    $review->id,
                    $review->unique_id,
                    $review->customer ? $review->customer->name : 'N/A',
                    $review->reviewer ? $review->reviewer->name : 'N/A',
                    $review->platform,
                    $review->business_name,
                    $review->amount,
                    $review->status,
                    $review->created_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

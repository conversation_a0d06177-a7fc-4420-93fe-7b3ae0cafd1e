<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function showLogin()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'role' => 'required|in:customer,reviewer'
        ]);

        $credentials = $request->only('email', 'password');
        
        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            // Check if user's role matches the selected role
            if ($user->role !== $request->role) {
                Auth::logout();
                return back()->withErrors(['email' => 'Selected role does not match your account role.']);
            }
            
            // Redirect based on role
            if ($user->role === 'admin') {
                return redirect()->route('admin.dashboard');
            } elseif ($user->role === 'customer') {
                return redirect()->route('dashboard.customer');
            } elseif ($user->role === 'reviewer') {
                return redirect()->route('dashboard.reviewer');
            } else {
                return redirect()->route('home');
            }
        }

        return back()->withErrors(['email' => 'Invalid credentials.']);
    }

    public function showRegister()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:customer,reviewer'
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role
        ]);

        Auth::login($user);

        // Redirect based on role
        if ($request->role === 'customer') {
            return redirect()->route('dashboard.customer');
        } else {
            return redirect()->route('dashboard.reviewer');
        }
    }

    public function showAdminLogin()
    {
        return view('auth.admin_login');
    }

    public function showAdminForgotPassword()
    {
        return view('auth.admin_forgot_password');
    }

    public function adminForgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        // Here you would typically send a password reset email
        // For now, we'll just redirect with a success message
        
        return redirect()->back()->with('success', 'If an admin account with that email exists, a password reset link has been sent.');
    }

    public function redirectToGoogle()
    {
        // This would typically redirect to Google OAuth
        // For now, we'll redirect to the login page
        return redirect()->route('login');
    }

    public function handleGoogleCallback(Request $request)
    {
        // This would typically handle the Google OAuth callback
        // For now, we'll redirect to the login page
        return redirect()->route('login');
    }

    public function adminLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required'
        ]);

        $credentials = $request->only('email', 'password');
        
        // Check if user exists and is admin
        $user = User::where('email', $credentials['email'])
                   ->whereIn('role', ['admin', 'super admin'])
                   ->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            return back()->withErrors(['email' => 'Invalid credentials or insufficient privileges.']);
        }

        Auth::login($user);
        return redirect()->route('admin.dashboard');
    }

    public function googleLogin(Request $request)
    {
        \Log::info('Google login attempt', $request->all());
        
        $request->validate([
            'email' => 'required|email',
            'name' => 'required|string',
            'google_id' => 'required|string',
            'role' => 'required|in:customer,reviewer'
        ]);

        try {
            DB::beginTransaction();

            // Check if user exists by email or google_id
            $user = User::where('email', $request->email)
                       ->orWhere('google_id', $request->google_id)
                       ->first();

            \Log::info('User lookup result', ['user' => $user ? $user->toArray() : null]);

            if ($user) {
                // Update existing user
                if (empty($user->google_id) && !empty($request->google_id)) {
                    $user->google_id = $request->google_id;
                }
                
                if ($user->name !== $request->name) {
                    $user->name = $request->name;
                }
                
                // Allow role switching
                if ($user->role !== $request->role) {
                    $user->role = $request->role;
                }
                
                $user->save();
                \Log::info('Updated existing user', ['user_id' => $user->id]);
            } else {
                // Create new user
                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'google_id' => $request->google_id,
                    'role' => $request->role,
                    'password' => Hash::make(Str::random(16)) // Random password for Google users
                ]);

                \Log::info('Created new user', ['user_id' => $user->id]);

                // Give signup bonus (you can implement this based on your wallet system)
                $this->giveSignupBonus($user->id, $request->role);
            }

            DB::commit();

            Auth::login($user);
            \Log::info('User logged in successfully', ['user_id' => $user->id, 'role' => $request->role]);

            // Determine the correct redirect URL
            $redirectUrl = '';
            if ($request->role === 'customer') {
                $redirectUrl = route('dashboard.customer');
            } elseif ($request->role === 'reviewer') {
                $redirectUrl = route('dashboard.reviewer');
            } else {
                $redirectUrl = route('home');
            }

            return response()->json([
                'success' => true,
                'redirect' => $redirectUrl,
                'message' => $user->wasRecentlyCreated ? 'Account created successfully!' : 'Login successful!',
                'user_role' => $request->role
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error("Google login error: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'error' => 'An error occurred during login. Please try again.'
            ], 500);
        }
    }

    private function giveSignupBonus($userId, $role)
    {
        // Implement signup bonus logic here
        // This would typically involve your wallet system
        // For now, we'll just log it
        \Log::info("Signup bonus given to user {$userId} with role {$role}");
    }

    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\Withdrawal;
use App\Models\PaymentRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
// QR Code package not available - using alternative

class WalletController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        $withdrawals = Withdrawal::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $pendingPayments = PaymentRequest::where('user_id', $user->id)
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->get();
            
        return view('wallet.index', compact('transactions', 'withdrawals', 'pendingPayments'));
    }

    public function addMoney(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:200|max:5000',
            'payment_method' => 'required|in:upi,qr_code,bank_transfer'
        ]);

        $user = Auth::user();
        $amount = $request->amount;
        $orderId = 'ORD' . time() . random_int(1000, 9999);

        // Generate UPI payment data
        $upiData = $this->generateUpiPaymentData($amount, $orderId, $user);

        // Create payment request
        $paymentRequest = PaymentRequest::create([
            'user_id' => $user->id,
            'order_id' => $orderId,
            'amount' => $amount,
            'payment_method' => $request->payment_method,
            'upi_data' => json_encode($upiData),
            'status' => 'pending',
            'expires_at' => now()->addMinutes(30) // 30 minute expiry
        ]);

        return response()->json([
            'success' => true,
            'payment_request_id' => $paymentRequest->id,
            'order_id' => $orderId,
            'amount' => $amount,
            'upi_data' => $upiData,
            'expires_at' => $paymentRequest->expires_at->format('Y-m-d H:i:s')
        ]);
    }

    public function generateUpiQr(Request $request)
    {
        $request->validate([
            'payment_request_id' => 'required|exists:payment_requests,id'
        ]);

        $paymentRequest = PaymentRequest::findOrFail($request->payment_request_id);
        
        if ($paymentRequest->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $upiData = json_decode($paymentRequest->upi_data, true);
        
        // Generate QR code using Google Charts API
        $qrCodeUrl = 'https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=' . urlencode($upiData['upi_url']);
        
        // For now, we'll use the external QR code service
        $qrPath = null; // We'll use the external URL directly

        return response()->json([
            'success' => true,
            'qr_code_url' => $qrCodeUrl,
            'upi_data' => $upiData
        ]);
    }

    public function submitPaymentProof(Request $request)
    {
        $request->validate([
            'payment_request_id' => 'required|exists:payment_requests,id',
            'transaction_id' => 'required|string|max:50',
            'payment_screenshot' => 'required|image|mimes:jpeg,png,jpg|max:5120',
            'payment_note' => 'nullable|string|max:500'
        ]);

        $paymentRequest = PaymentRequest::findOrFail($request->payment_request_id);
        
        if ($paymentRequest->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($paymentRequest->status !== 'pending') {
            return response()->json(['error' => 'Payment request is no longer pending'], 400);
        }

        // Store payment screenshot
        $screenshotPath = $request->file('payment_screenshot')->store('payment_proofs', 'public');

        // Update payment request
        $paymentRequest->update([
            'transaction_id' => $request->transaction_id,
            'payment_screenshot' => $screenshotPath,
            'payment_note' => $request->payment_note,
            'status' => 'verification_pending',
            'submitted_at' => now()
        ]);

        // Send notification to admin (you can implement email/SMS here)
        $this->notifyAdminNewPayment($paymentRequest);

        return response()->json([
            'success' => true,
            'message' => 'Payment proof submitted successfully! Admin will verify and add money to your wallet within 24 hours.'
        ]);
    }

    public function requestWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:100|max:50000',
            'upi_id' => 'required|string|max:100',
            'whatsapp' => 'required|string|max:20'
        ]);

        $user = Auth::user();
        
        if ($user->wallet_balance < $request->amount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient wallet balance'
            ], 400);
        }

        $withdrawal = Withdrawal::create([
            'user_id' => $user->id,
            'amount' => $request->amount,
            'upi_id' => $request->upi_id,
            'whatsapp' => $request->whatsapp,
            'status' => 'pending'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully!'
        ]);
    }

    public function myWithdrawals()
    {
        $withdrawals = Withdrawal::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return view('wallet.withdrawals', compact('withdrawals'));
    }

    // Admin methods
    public function approvePayment(Request $request, $id)
    {
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $paymentRequest = PaymentRequest::findOrFail($id);
        
        if ($paymentRequest->status !== 'verification_pending') {
            return response()->json(['error' => 'Payment is not pending verification'], 400);
        }

        try {
            \DB::beginTransaction();

            // Add money to user wallet
            $user = $paymentRequest->user;
            $user->increment('wallet_balance', $paymentRequest->amount);

            // Create wallet transaction
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => 'credit',
                'amount' => $paymentRequest->amount,
                'description' => "Wallet recharge - Order #{$paymentRequest->order_id}",
                'reference_id' => $paymentRequest->id,
                'reference_type' => 'payment_request',
                'status' => 'completed'
            ]);

            // Update payment request
            $paymentRequest->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
                'admin_note' => $request->admin_note
            ]);

            \DB::commit();

            // Send confirmation notification to user
            $this->notifyUserPaymentApproved($paymentRequest);

            return response()->json([
                'success' => true,
                'message' => 'Payment approved and wallet credited successfully!'
            ]);

        } catch (\Exception $e) {
            \DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error processing payment approval'
            ], 500);
        }
    }

    public function rejectPayment(Request $request, $id)
    {
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:500'
        ]);

        $paymentRequest = PaymentRequest::findOrFail($id);
        
        $paymentRequest->update([
            'status' => 'rejected',
            'rejected_by' => Auth::id(),
            'rejected_at' => now(),
            'rejection_reason' => $request->rejection_reason
        ]);

        // Notify user about rejection
        $this->notifyUserPaymentRejected($paymentRequest);

        return response()->json([
            'success' => true,
            'message' => 'Payment rejected successfully'
        ]);
    }

    public function approveWithdrawal($id)
    {
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $withdrawal = Withdrawal::findOrFail($id);
        
        try {
            \DB::beginTransaction();

            // Deduct from user wallet
            $user = $withdrawal->user;
            $user->decrement('wallet_balance', $withdrawal->amount);

            // Create transaction record
            WalletTransaction::create([
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $withdrawal->amount,
                'description' => "Withdrawal to {$withdrawal->upi_id}",
                'reference_id' => $withdrawal->id,
                'reference_type' => 'withdrawal'
            ]);

            // Update withdrawal status
            $withdrawal->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now()
            ]);

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal approved successfully!'
            ]);

        } catch (\Exception $e) {
            \DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error processing withdrawal'
            ], 500);
        }
    }

    public function rejectWithdrawal(Request $request, $id)
    {
        if (!Auth::user()->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $withdrawal = Withdrawal::findOrFail($id);
        
        $withdrawal->update([
            'status' => 'rejected',
            'rejected_by' => Auth::id(),
            'rejected_at' => now(),
            'rejection_reason' => $request->rejection_reason
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Withdrawal rejected successfully'
        ]);
    }

    // Private helper methods
    private function generateUpiPaymentData($amount, $orderId, $user)
    {
        // Your UPI ID (replace with your actual UPI ID)
        $merchantUpiId = env('MERCHANT_UPI_ID', 'your-upi@paytm'); // Add this to .env
        $merchantName = env('MERCHANT_NAME', 'ReviewPlatform');
        
        // Generate UPI URL for payment
        $upiUrl = "upi://pay?pa={$merchantUpiId}&pn={$merchantName}&am={$amount}&tr={$orderId}&tn=Wallet Recharge Order {$orderId}&cu=INR";
        
        // Generate UPI intent URL (for mobile apps)
        $intentUrl = "intent://pay?pa={$merchantUpiId}&pn={$merchantName}&am={$amount}&tr={$orderId}&tn=Wallet Recharge Order {$orderId}&cu=INR#Intent;scheme=upi;package=com.google.android.apps.nbu.paisa.user;end";
        
        return [
            'upi_id' => $merchantUpiId,
            'merchant_name' => $merchantName,
            'amount' => $amount,
            'order_id' => $orderId,
            'upi_url' => $upiUrl,
            'intent_url' => $intentUrl,
            'note' => "Wallet Recharge Order {$orderId}"
        ];
    }

    private function notifyAdminNewPayment($paymentRequest)
    {
        // TODO: Implement admin notification
        // You can send email, SMS, or push notification here
        \Log::info("New payment submission for approval", [
            'order_id' => $paymentRequest->order_id,
            'amount' => $paymentRequest->amount,
            'user' => $paymentRequest->user->name,
            'email' => $paymentRequest->user->email
        ]);
    }

    private function notifyUserPaymentApproved($paymentRequest)
    {
        // TODO: Implement user notification for approval
        \Log::info("Payment approved for user", [
            'order_id' => $paymentRequest->order_id,
            'amount' => $paymentRequest->amount,
            'user' => $paymentRequest->user->name
        ]);
    }

    private function notifyUserPaymentRejected($paymentRequest)
    {
        // TODO: Implement user notification for rejection
        \Log::info("Payment rejected for user", [
            'order_id' => $paymentRequest->order_id,
            'amount' => $paymentRequest->amount,
            'user' => $paymentRequest->user->name,
            'reason' => $paymentRequest->rejection_reason
        ]);
    }
} 
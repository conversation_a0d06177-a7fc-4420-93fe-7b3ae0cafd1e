<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'amount',
        'payment_method',
        'upi_data',
        'transaction_id',
        'payment_screenshot',
        'payment_note',
        'status',
        'expires_at',
        'submitted_at',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'rejection_reason',
        'admin_note'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejectedBy()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'secondary',
            'verification_pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'expired' => 'dark'
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getUpiDataAttribute($value)
    {
        return json_decode($value, true);
    }

    public function setUpiDataAttribute($value)
    {
        $this->attributes['upi_data'] = json_encode($value);
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isPending()
    {
        return $this->status === 'pending' && !$this->isExpired();
    }

    public function isVerificationPending()
    {
        return $this->status === 'verification_pending';
    }

    public function isApproved()
    {
        return $this->status === 'approved';
    }

    public function isRejected()
    {
        return $this->status === 'rejected';
    }
} 
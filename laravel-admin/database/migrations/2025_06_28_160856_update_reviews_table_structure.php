<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['reviewer_id']);

            // Drop old columns
            $table->dropColumn([
                'customer_id',
                'reviewer_id',
                'country',
                'review_count',
                'rating',
                'notes',
                'total_amount',
                'screenshot_url',
                'submitted_at'
            ]);

            // Add new columns
            $table->unsignedBigInteger('user_id')->after('id');
            $table->unsignedBigInteger('claimed_by')->nullable()->after('user_id');
            $table->text('review_text')->after('business_url');
            $table->string('screenshot_path')->after('review_text');
            $table->string('upi_qr_path')->after('screenshot_path');

            // Add foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('claimed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            // Drop foreign keys
            $table->dropForeign(['user_id']);
            $table->dropForeign(['claimed_by']);

            // Drop new columns
            $table->dropColumn([
                'user_id',
                'claimed_by',
                'review_text',
                'screenshot_path',
                'upi_qr_path'
            ]);

            // Add back old columns
            $table->unsignedBigInteger('customer_id')->after('id');
            $table->unsignedBigInteger('reviewer_id')->nullable()->after('customer_id');
            $table->string('country')->after('platform');
            $table->integer('review_count')->after('business_url');
            $table->decimal('rating', 3, 2)->after('review_count');
            $table->text('notes')->after('rating');
            $table->decimal('total_amount', 10, 2)->after('amount');
            $table->string('screenshot_url')->after('status');
            $table->timestamp('submitted_at')->nullable()->after('claimed_at');

            // Add foreign key constraints
            $table->foreign('customer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('set null');
        });
    }
};

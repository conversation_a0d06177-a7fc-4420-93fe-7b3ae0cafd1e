<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['customer', 'reviewer', 'admin', 'super admin'])->default('customer')->after('email');
            $table->decimal('wallet_balance', 10, 2)->default(0.00)->after('role');
            $table->string('whatsapp')->nullable()->after('wallet_balance');
            $table->string('upi_id')->nullable()->after('whatsapp');
            $table->enum('status', ['active', 'banned'])->default('active')->after('upi_id');
            $table->boolean('signup_bonus_given')->default(false)->after('status');
            $table->string('reset_token')->nullable()->after('signup_bonus_given');
            $table->timestamp('reset_expires')->nullable()->after('reset_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role', 'wallet_balance', 'whatsapp', 'upi_id', 
                'status', 'signup_bonus_given', 'reset_token', 'reset_expires'
            ]);
        });
    }
};

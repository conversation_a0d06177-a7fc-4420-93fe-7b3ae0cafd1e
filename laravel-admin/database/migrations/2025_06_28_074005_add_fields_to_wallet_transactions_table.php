<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->unsignedBigInteger('reference_id')->nullable()->after('description');
            $table->string('reference_type')->nullable()->after('reference_id');
            $table->string('payment_method')->nullable()->after('reference_type');
            $table->enum('status', ['pending', 'completed', 'failed'])->default('completed')->after('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'reference_id',
                'reference_type',
                'payment_method',
                'status'
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->string('platform')->nullable()->after('reviewer_id');
            $table->string('country')->nullable()->after('platform');
            $table->string('business_name')->nullable()->after('country');
            $table->text('business_url')->nullable()->after('business_name');
            $table->integer('review_count')->default(1)->after('business_url');
            $table->integer('rating')->default(5)->after('review_count');
            $table->text('notes')->nullable()->after('rating');
            $table->decimal('total_amount', 10, 2)->nullable()->after('notes');
            $table->timestamp('claimed_at')->nullable()->after('rejection_reason');
            $table->timestamp('submitted_at')->nullable()->after('claimed_at');
            $table->timestamp('completed_at')->nullable()->after('submitted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn([
                'platform',
                'country',
                'business_name',
                'business_url',
                'review_count',
                'rating',
                'notes',
                'total_amount',
                'claimed_at',
                'submitted_at',
                'completed_at'
            ]);
        });
    }
};

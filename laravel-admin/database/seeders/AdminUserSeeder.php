<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('Harman1313@'),
            'role' => 'admin',
            'wallet_balance' => 0.00,
            'status' => 'active',
        ]);
        
        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: Harman1313@');
    }
}

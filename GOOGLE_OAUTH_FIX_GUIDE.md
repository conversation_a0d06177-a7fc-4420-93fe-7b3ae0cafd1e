# Google OAuth 2.0 Error Fix Guide

## 🚨 Current Error
```
You can't sign in because this app sent an invalid request.
Error 400: redirect_uri_mismatch
```

## 🔍 Root Cause Analysis
The error occurs because your Google Cloud Console OAuth 2.0 client is not configured to allow requests from your current application origin.

**Current Configuration:**
- **App URL**: `http://127.0.0.1:8080`
- **Google Client ID**: `136943631858-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com`

## ✅ IMMEDIATE FIX REQUIRED

### Step 1: Update Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services** > **Credentials**
3. Find your OAuth 2.0 client ID: `136943631858-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com`
4. Click on it to edit

### Step 2: Add Authorized JavaScript Origins
In the **Authorized JavaScript origins** section, add these URLs:
```
http://127.0.0.1:8080
http://localhost:8080
```

### Step 3: Add Authorized Redirect URIs
In the **Authorized redirect URIs** section, add these **exact URLs**:
```
http://127.0.0.1:8080
http://localhost:8080
http://127.0.0.1:8080/
http://localhost:8080/
```

**Note**: For Google Sign-In with popup mode, the redirect URI is typically the base URL of your application.

### Step 4: Save and Wait
1. Click **Save**
2. Wait 5-10 minutes for changes to propagate

## 🔧 Code Fixes Applied

### Fixed Files:
1. **laravel-admin/resources/views/auth/login.blade.php**
   - Updated to use environment variables instead of hardcoded values
   - Fixed origin to use `APP_URL` from environment

2. **includes/footer.php**
   - Updated to use environment variables
   - Fixed origin configuration

3. **laravel-admin/google-oauth-debug.html**
   - Updated to use correct Client ID

## 🧪 Testing Steps

### 1. Test with Debug Page
Open: `http://127.0.0.1:8080/laravel-admin/google-oauth-debug.html`

### 2. Test Main Application
1. Start your server: `php -S 127.0.0.1:8080`
2. Open: `http://127.0.0.1:8080`
3. Try Google Sign-In

### 3. Check Browser Console
- Open Developer Tools (F12)
- Look for any JavaScript errors
- Verify the Google Sign-In button loads

## 🚀 Production Setup (When Ready)

For production deployment, add these to Google Cloud Console:
```
https://yourdomain.com
https://www.yourdomain.com
https://yourdomain.com/auth/google/callback
https://www.yourdomain.com/auth/google/callback
```

## 🔒 Security Notes
- Never commit `.env` files to version control
- Use different Client IDs for development and production
- Regularly rotate OAuth credentials
- Monitor OAuth usage in Google Cloud Console

## 📞 Support
If the issue persists after following these steps:
1. Check browser console for errors
2. Verify the Client ID is correct
3. Ensure you're using the exact URLs listed above
4. Clear browser cache and try again

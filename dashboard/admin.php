<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'admin') {
    header('Location: /index.php');
    exit;
}
require_once '../includes/db.php';
require_once '../includes/wallet.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle user creation
if (isset($_POST['create_user'])) {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $role = $_POST['role'];
    $whatsapp = trim($_POST['whatsapp'] ?? '');
    $upi_id = trim($_POST['upi_id'] ?? '');
    $password = $_POST['password'];
    
    if (empty($name) || empty($email) || empty($password)) {
        set_flash('<div class="alert alert-danger">All required fields must be filled.</div>');
    } else {
        try {
            // Check if email already exists
            $stmt = $pdo->prepare('SELECT id FROM users WHERE email = ?');
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                set_flash('<div class="alert alert-danger">Email already exists.</div>');
            } else {
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare('INSERT INTO users (name, email, password, role, whatsapp, upi_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, "active", NOW())');
                $stmt->execute([$name, $email, $hashed_password, $role, $whatsapp, $upi_id]);
                
                set_flash('<div class="alert alert-success">User created successfully!</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error creating user: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle user deletion
if (isset($_POST['delete_user'])) {
    $user_id = intval($_POST['user_id']);
    $confirmation = $_POST['confirmation'];
    
    if ($confirmation === 'DELETE') {
        $pdo->beginTransaction();
        try {
            // Check if user has any reviews
            $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE customer_id = ? OR reviewer_id = ?');
            $stmt->execute([$user_id, $user_id]);
            $review_count = $stmt->fetch()['count'];
            
            if ($review_count > 0) {
                set_flash('<div class="alert alert-warning">Cannot delete user with existing reviews. Consider banning instead.</div>');
            } else {
                // Delete user's wallet transactions
                $stmt = $pdo->prepare('DELETE FROM wallet_transactions WHERE user_id = ?');
                $stmt->execute([$user_id]);
                
                // Delete user's withdrawals
                $stmt = $pdo->prepare('DELETE FROM withdrawals WHERE user_id = ?');
                $stmt->execute([$user_id]);
                
                // Delete the user
                $stmt = $pdo->prepare('DELETE FROM users WHERE id = ? AND role != "admin"');
                $stmt->execute([$user_id]);
                
                if ($stmt->rowCount() > 0) {
                    set_flash('<div class="alert alert-success">User deleted successfully!</div>');
                } else {
                    set_flash('<div class="alert alert-danger">User not found or cannot delete admin.</div>');
                }
            }
            $pdo->commit();
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error deleting user: ' . $e->getMessage() . '</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">Please type DELETE to confirm user deletion.</div>');
    }
    header('Location: admin.php');
    exit;
}

// Handle password reset for users
if (isset($_POST['reset_user_password'])) {
    $user_id = intval($_POST['user_id']);
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($new_password) || strlen($new_password) < 6) {
        set_flash('<div class="alert alert-danger">Password must be at least 6 characters long.</div>');
    } elseif ($new_password !== $confirm_password) {
        set_flash('<div class="alert alert-danger">Passwords do not match.</div>');
    } else {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare('UPDATE users SET password = ? WHERE id = ? AND role != "admin"');
            $stmt->execute([$hashed_password, $user_id]);
            
            if ($stmt->rowCount() > 0) {
                set_flash('<div class="alert alert-success">User password reset successfully!</div>');
            } else {
                set_flash('<div class="alert alert-danger">User not found or cannot reset admin password.</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error resetting password: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle admin password change
if (isset($_POST['change_admin_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        set_flash('<div class="alert alert-danger">All password fields are required.</div>');
    } elseif (strlen($new_password) < 6) {
        set_flash('<div class="alert alert-danger">New password must be at least 6 characters long.</div>');
    } elseif ($new_password !== $confirm_password) {
        set_flash('<div class="alert alert-danger">New passwords do not match.</div>');
    } else {
        try {
            // Verify current password
            $stmt = $pdo->prepare('SELECT password FROM users WHERE id = ? AND role = "admin"');
            $stmt->execute([$user['id']]);
            $admin = $stmt->fetch();
            
            if (!$admin || !password_verify($current_password, $admin['password'])) {
                set_flash('<div class="alert alert-danger">Current password is incorrect.</div>');
            } else {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare('UPDATE users SET password = ? WHERE id = ? AND role = "admin"');
                $stmt->execute([$hashed_password, $user['id']]);
                
                set_flash('<div class="alert alert-success">Admin password changed successfully!</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error changing password: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle set initial password for admin
if (isset($_POST['set_initial_password'])) {
    $admin_id = intval($_POST['admin_id']);
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($new_password) || strlen($new_password) < 6) {
        set_flash('<div class="alert alert-danger">Password must be at least 6 characters long.</div>');
    } elseif ($new_password !== $confirm_password) {
        set_flash('<div class="alert alert-danger">Passwords do not match.</div>');
    } else {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare('UPDATE users SET password = ? WHERE id = ? AND role = "admin"');
            $stmt->execute([$hashed_password, $admin_id]);
            
            if ($stmt->rowCount() > 0) {
                set_flash('<div class="alert alert-success">Initial password set successfully!</div>');
            } else {
                set_flash('<div class="alert alert-danger">Admin not found or already has a password.</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error setting password: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Analytics functions
function getRevenueData($pdo, $days = 30) {
    $stmt = $pdo->prepare('
        SELECT DATE(created_at) as date, SUM(amount) as revenue, COUNT(*) as reviews
        FROM reviews 
        WHERE status = "approved" AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ');
    $stmt->execute([$days]);
    return $stmt->fetchAll();
}

function getUserGrowthData($pdo, $days = 30) {
    $stmt = $pdo->prepare('
        SELECT DATE(created_at) as date, COUNT(*) as new_users
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ');
    $stmt->execute([$days]);
    return $stmt->fetchAll();
}

function getDashboardStats($pdo) {
    $stats = [];
    
    // Total revenue
    $stmt = $pdo->prepare('SELECT SUM(amount) as total FROM reviews WHERE status = "approved"');
    $stmt->execute();
    $stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;
    
    // Today's revenue
    $stmt = $pdo->prepare('SELECT SUM(amount) as total FROM reviews WHERE status = "approved" AND DATE(created_at) = CURDATE()');
    $stmt->execute();
    $stats['today_revenue'] = $stmt->fetch()['total'] ?? 0;
    
    // Total users
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM users WHERE role != "admin"');
    $stmt->execute();
    $stats['total_users'] = $stmt->fetch()['count'];
    
    // New users today
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM users WHERE role != "admin" AND DATE(created_at) = CURDATE()');
    $stmt->execute();
    $stats['new_users_today'] = $stmt->fetch()['count'];
    
    // Total reviews
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews');
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['total_reviews'] = $result ? intval($result['count']) : 0;
    
    // Reviews today
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE DATE(created_at) = CURDATE()');
    $stmt->execute();
    $result = $stmt->fetch();
    $stats['reviews_today'] = $result ? intval($result['count']) : 0;
    
    // Completion rate
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE status IN ("approved", "rejected")');
    $stmt->execute();
    $result = $stmt->fetch();
    $completed = $result ? intval($result['count']) : 0;
    $stats['completion_rate'] = $stats['total_reviews'] > 0 ? round(($completed / $stats['total_reviews']) * 100, 1) : 0;
    
    return $stats;
}

// Handle review approval/rejection
if (isset($_POST['review_action'])) {
    $review_id = intval($_POST['review_id']);
    $action = $_POST['action'];
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    
    if (in_array($action, ['approve', 'reject'])) {
        $pdo->beginTransaction();
        try {
            if ($action === 'approve') {
                // Get review details
                $stmt = $pdo->prepare('SELECT r.*, u.wallet_balance FROM reviews r JOIN users u ON r.reviewer_id = u.id WHERE r.id = ?');
    $stmt->execute([$review_id]);
    $review = $stmt->fetch();
                
    if ($review) {
                    // Update review status
                    $stmt = $pdo->prepare('UPDATE reviews SET status = "approved" WHERE id = ?');
                    $stmt->execute([$review_id]);
                    
                    // Add earnings to reviewer (80% of amount)
                    $earnings = $review['amount'] * 0.8;
                $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?');
                    $stmt->execute([$earnings, $review['reviewer_id']]);
                    
                    // Record transaction
                    $stmt = $pdo->prepare('INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference) VALUES (?, "credit", ?, ?, ?)');
                    $stmt->execute([$review['reviewer_id'], $earnings, $review_id, 'Review approved']);
                    
                    // Delete the screenshot file to save storage
                    if ($review['screenshot_url']) {
                        $filepath = __DIR__ . '/../' . ltrim($review['screenshot_url'], '/');
                        if (file_exists($filepath)) {
                            unlink($filepath);
                        }
                        // Update database to remove screenshot URL
                        $stmt = $pdo->prepare('UPDATE reviews SET screenshot_url = NULL WHERE id = ?');
                        $stmt->execute([$review_id]);
                    }
                    
                    set_flash('<div class="alert alert-success">Review approved! Reviewer earned $' . number_format($earnings, 2) . '</div>');
                }
            } else {
                // Reject review
                $stmt = $pdo->prepare('UPDATE reviews SET status = "rejected" WHERE id = ?');
                $stmt->execute([$review_id]);
                
                // Release review back to available pool
                $stmt = $pdo->prepare('UPDATE reviews SET reviewer_id = NULL, status = "open" WHERE id = ?');
                $stmt->execute([$review_id]);
                
                // Delete the screenshot file to save storage
                $stmt = $pdo->prepare('SELECT screenshot_url FROM reviews WHERE id = ?');
                $stmt->execute([$review_id]);
                $review = $stmt->fetch();
                
                if ($review && $review['screenshot_url']) {
                    $filepath = __DIR__ . '/../' . ltrim($review['screenshot_url'], '/');
                    if (file_exists($filepath)) {
                        unlink($filepath);
                    }
                    // Update database to remove screenshot URL
                    $stmt = $pdo->prepare('UPDATE reviews SET screenshot_url = NULL WHERE id = ?');
                    $stmt->execute([$review_id]);
                }
                
                set_flash('<div class="alert alert-warning">Review rejected and returned to available pool.</div>');
            }
            
                $pdo->commit();
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error processing review: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle user ban/unban
if (isset($_POST['user_action'])) {
    $user_id = intval($_POST['user_id']);
    $action = $_POST['action'];
    
    if (in_array($action, ['ban', 'unban'])) {
        try {
            if ($action === 'ban') {
                $stmt = $pdo->prepare('UPDATE users SET status = "banned" WHERE id = ?');
                $stmt->execute([$user_id]);
                set_flash('<div class="alert alert-warning">User banned successfully.</div>');
            } else {
                $stmt = $pdo->prepare('UPDATE users SET status = "active" WHERE id = ?');
                $stmt->execute([$user_id]);
                set_flash('<div class="alert alert-success">User unbanned successfully.</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error processing user action: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle withdrawal requests
if (isset($_POST['withdrawal_action'])) {
    $withdrawal_id = intval($_POST['withdrawal_id']);
    $action = $_POST['action'];
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    
    if (in_array($action, ['approve', 'reject'])) {
        $pdo->beginTransaction();
        try {
            // Get withdrawal details
            $stmt = $pdo->prepare('SELECT * FROM withdrawals WHERE id = ?');
            $stmt->execute([$withdrawal_id]);
            $withdrawal = $stmt->fetch();
            
            if ($withdrawal) {
                if ($action === 'approve') {
                    $stmt = $pdo->prepare('UPDATE withdrawals SET status = "approved" WHERE id = ?');
                    $stmt->execute([$withdrawal_id]);
                    set_flash('<div class="alert alert-success">Withdrawal approved successfully.</div>');
                } else {
                    // Reject and refund to user wallet
                    $stmt = $pdo->prepare('UPDATE withdrawals SET status = "rejected", rejection_reason = ? WHERE id = ?');
                    $stmt->execute([$rejection_reason, $withdrawal_id]);
                    
                    $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?');
                    $stmt->execute([$withdrawal['amount'], $withdrawal['user_id']]);
                    
                    set_flash('<div class="alert alert-warning">Withdrawal rejected and amount refunded to user wallet.</div>');
                }
            }
            
            $pdo->commit();
        } catch (Exception $e) {
                $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error processing withdrawal: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle storage cleanup
if (isset($_POST['cleanup_storage'])) {
    $cleanup_type = $_POST['cleanup_type'];
    $deleted_count = 0;
    $deleted_size = 0;
    
    try {
        if ($cleanup_type === 'old_reviews') {
            // Delete old approved/rejected review images (older than 24 hours)
            $stmt = $pdo->prepare('SELECT screenshot_url FROM reviews WHERE status IN ("approved", "rejected") AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND screenshot_url IS NOT NULL');
            $stmt->execute();
            $old_reviews = $stmt->fetchAll();
            
            foreach ($old_reviews as $review) {
                $filepath = __DIR__ . '/../' . ltrim($review['screenshot_url'], '/');
                if (file_exists($filepath)) {
                    $deleted_size += filesize($filepath);
                    unlink($filepath);
                    $deleted_count++;
                }
            }
            
            // Update database to remove screenshot URLs
            $stmt = $pdo->prepare('UPDATE reviews SET screenshot_url = NULL WHERE status IN ("approved", "rejected") AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)');
            $stmt->execute();
            
            set_flash('<div class="alert alert-success">Cleaned up ' . $deleted_count . ' old review images. Freed ' . formatBytes($deleted_size) . ' of storage.</div>');
            
        } elseif ($cleanup_type === 'all_images') {
            // Delete all review images (use with caution)
            $stmt = $pdo->prepare('SELECT screenshot_url FROM reviews WHERE screenshot_url IS NOT NULL');
            $stmt->execute();
            $all_reviews = $stmt->fetchAll();
            
            foreach ($all_reviews as $review) {
                $filepath = __DIR__ . '/../' . ltrim($review['screenshot_url'], '/');
                if (file_exists($filepath)) {
                    $deleted_size += filesize($filepath);
                    unlink($filepath);
                    $deleted_count++;
                }
            }
            
            // Update database to remove all screenshot URLs
            $stmt = $pdo->prepare('UPDATE reviews SET screenshot_url = NULL WHERE screenshot_url IS NOT NULL');
            $stmt->execute();
            
            set_flash('<div class="alert alert-warning">Deleted all review images (' . $deleted_count . ' files). Freed ' . formatBytes($deleted_size) . ' of storage.</div>');
        }
        
    } catch (Exception $e) {
        set_flash('<div class="alert alert-danger">Error during cleanup: ' . $e->getMessage() . '</div>');
    }
    
    header('Location: admin.php');
    exit;
}

// Handle services management
if (isset($_POST['service_action'])) {
    $service_id = intval($_POST['service_id']);
    $action = $_POST['action'];
    
    if (in_array($action, ['update', 'toggle_status'])) {
        try {
            if ($action === 'update') {
                $min_price = floatval($_POST['min_price']);
                $max_price = floatval($_POST['max_price']);
                $default_price = floatval($_POST['default_price']);
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                // Validate prices
                if ($min_price < 0 || $max_price < 0 || $default_price < 0) {
                    throw new Exception('Prices cannot be negative');
                }
                if ($min_price > $max_price) {
                    throw new Exception('Minimum price cannot be greater than maximum price');
                }
                if ($default_price < $min_price || $default_price > $max_price) {
                    throw new Exception('Default price must be between minimum and maximum prices');
                }
                
                $stmt = $pdo->prepare('UPDATE services SET min_price = ?, max_price = ?, default_price = ?, is_active = ? WHERE id = ?');
                $stmt->execute([$min_price, $max_price, $default_price, $is_active, $service_id]);
                
                set_flash('<div class="alert alert-success">Service updated successfully!</div>');
                
            } elseif ($action === 'toggle_status') {
                $stmt = $pdo->prepare('UPDATE services SET is_active = NOT is_active WHERE id = ?');
                $stmt->execute([$service_id]);
                
                set_flash('<div class="alert alert-success">Service status toggled successfully!</div>');
            }
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error updating service: ' . $e->getMessage() . '</div>');
        }
    }
    header('Location: admin.php');
    exit;
}

// Handle currency settings update
if (isset($_POST['update_currency_settings'])) {
    $currency = trim($_POST['currency'] ?? '');
    $currency_symbol = trim($_POST['currency_symbol'] ?? '');
    $signup_bonus_customer = floatval($_POST['signup_bonus_customer'] ?? 0);
    $signup_bonus_reviewer = floatval($_POST['signup_bonus_reviewer'] ?? 0);
    $minimum_payout = floatval($_POST['minimum_payout'] ?? 0);
    $platform_fee_percentage = floatval($_POST['platform_fee_percentage'] ?? 0);
    
    $errors = [];
    if (empty($currency)) $errors[] = 'Currency code is required';
    if (empty($currency_symbol)) $errors[] = 'Currency symbol is required';
    if ($signup_bonus_customer < 0) $errors[] = 'Customer signup bonus cannot be negative';
    if ($signup_bonus_reviewer < 0) $errors[] = 'Reviewer signup bonus cannot be negative';
    if ($minimum_payout <= 0) $errors[] = 'Minimum payout must be greater than 0';
    if ($platform_fee_percentage < 0 || $platform_fee_percentage > 100) $errors[] = 'Platform fee must be between 0-100%';
    
    if (empty($errors)) {
        try {
            // Update all settings
            update_setting('currency', $currency);
            update_setting('currency_symbol', $currency_symbol);
            update_setting('signup_bonus_customer', number_format($signup_bonus_customer, 2, '.', ''));
            update_setting('signup_bonus_reviewer', number_format($signup_bonus_reviewer, 2, '.', ''));
            update_setting('minimum_payout', number_format($minimum_payout, 2, '.', ''));
            update_setting('platform_fee_percentage', number_format($platform_fee_percentage, 2, '.', ''));
            
            set_flash('<div class="alert alert-success">Currency settings updated successfully!</div>');
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error updating currency settings: ' . $e->getMessage() . '</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">' . implode('<br>', $errors) . '</div>');
    }
    header('Location: admin.php');
    exit;
}

// Helper function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Calculate storage statistics
function getStorageStats($pdo) {
    $stats = [];
    
    // Count total reviews with images
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE screenshot_url IS NOT NULL');
    $stmt->execute();
    $stats['total_images'] = $stmt->fetch()['count'];
    
    // Count pending images
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE status = "submitted" AND screenshot_url IS NOT NULL');
    $stmt->execute();
    $stats['pending_images'] = $stmt->fetch()['count'];
    
    // Count old images (older than 24 hours) - using created_at since submitted_at doesn't exist
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE status IN ("approved", "rejected") AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND screenshot_url IS NOT NULL');
    $stmt->execute();
    $stats['old_images'] = $stmt->fetch()['count'];
    
    // Calculate total storage size
    $stmt = $pdo->prepare('SELECT screenshot_url FROM reviews WHERE screenshot_url IS NOT NULL');
    $stmt->execute();
    $reviews = $stmt->fetchAll();
    
    $total_size = 0;
    foreach ($reviews as $review) {
        $filepath = __DIR__ . '/../' . ltrim($review['screenshot_url'], '/');
        if (file_exists($filepath)) {
            $total_size += filesize($filepath);
        }
    }
    $stats['total_size'] = $total_size;
    
    return $stats;
}

$storage_stats = getStorageStats($pdo);

// Get analytics data
$revenue_data = getRevenueData($pdo, 30);
$user_growth_data = getUserGrowthData($pdo, 30);
$dashboard_stats = getDashboardStats($pdo);

// Fetch pending reviews
$stmt = $pdo->prepare('
    SELECT r.*, 
           c.name as customer_name, c.email as customer_email,
           rev.name as reviewer_name, rev.email as reviewer_email, rev.whatsapp as reviewer_whatsapp
    FROM reviews r 
    JOIN users c ON r.customer_id = c.id 
    LEFT JOIN users rev ON r.reviewer_id = rev.id 
    WHERE r.status = "submitted" 
    ORDER BY r.created_at ASC
');
$stmt->execute();
$pending_reviews = $stmt->fetchAll();

// Fetch all users
$stmt = $pdo->prepare('
    SELECT u.*, 
           COUNT(r.id) as total_reviews,
           SUM(CASE WHEN r.status = "approved" THEN 1 ELSE 0 END) as approved_reviews,
           SUM(CASE WHEN r.status = "rejected" THEN 1 ELSE 0 END) as rejected_reviews
    FROM users u 
    LEFT JOIN reviews r ON u.id = r.reviewer_id 
    WHERE u.role != "admin"
    GROUP BY u.id 
    ORDER BY u.created_at DESC
');
$stmt->execute();
$users = $stmt->fetchAll();

// Fetch pending withdrawals
$stmt = $pdo->prepare('
    SELECT w.*, u.name as user_name, u.email as user_email, u.whatsapp, u.upi_id
    FROM withdrawals w 
    JOIN users u ON w.user_id = u.id 
    WHERE w.status = "pending" 
    ORDER BY w.created_at ASC
');
$stmt->execute();
$pending_withdrawals = $stmt->fetchAll();

// Fetch all services
$stmt = $pdo->prepare('SELECT * FROM services ORDER BY display_name ASC');
$stmt->execute();
$services = $stmt->fetchAll();

// Dashboard stats
$stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE status = "submitted"');
$stmt->execute();
$pending_reviews_count = $stmt->fetch()['count'];

$stmt = $pdo->prepare('SELECT COUNT(*) as count FROM withdrawals WHERE status = "pending"');
$stmt->execute();
$pending_withdrawals_count = $stmt->fetch()['count'];

$stmt = $pdo->prepare('SELECT COUNT(*) as count FROM users WHERE status = "banned"');
$stmt->execute();
$banned_users_count = $stmt->fetch()['count'];

$stmt = $pdo->prepare('SELECT SUM(amount) as total FROM reviews WHERE status = "approved"');
$stmt->execute();
$total_earnings = $stmt->fetch()['total'] ?? 0;

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .btn-success {
            background: var(--success-color);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-danger {
            background: var(--danger-color);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-warning {
            background: var(--warning-color);
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .review-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .review-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .platform-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .platform-google { background: #4285f4; color: white; }
        .platform-yelp { background: #ff1a1a; color: white; }
        .platform-facebook { background: #1877f2; color: white; }
        .platform-tripadvisor { background: #00aa6c; color: white; }
        .platform-trustpilot { background: #00b67a; color: white; }
        .platform-other { background: #6b7280; color: white; }
        
        .amount-badge {
            background: var(--success-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .user-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .user-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: capitalize;
        }
        
        .role-customer {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-reviewer {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-active { background: #dcfce7; color: #166534; }
        .status-banned { background: #fecaca; color: #991b1b; }
        
        .withdrawal-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .withdrawal-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .nav-pills .nav-link {
            border-radius: 12px;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .nav-pills .nav-link.active {
            background: var(--primary-color);
        }
        
        .screenshot-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .modal-content {
            border-radius: 16px;
            border: none;
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background: var(--bg-light);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }
        
        /* Analytics Dashboard Styles */
        .activity-timeline {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            padding: 0.75rem;
            border-left: 3px solid var(--primary-color);
            background: var(--bg-light);
            margin-bottom: 0.5rem;
            border-radius: 0 8px 8px 0;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: white;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        /* Enhanced stats cards */
        .stats-card {
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        /* Modal enhancements */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0;
            background: var(--bg-light);
        }
        
        .modal-footer {
            border-top: 1px solid var(--border-color);
            border-radius: 0 0 16px 16px;
            background: var(--bg-light);
        }
        
        /* Password Management Styles */
        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .password-strength.weak {
            background: var(--danger-color);
            width: 25%;
        }
        
        .password-strength.fair {
            background: var(--warning-color);
            width: 50%;
        }
        
        .password-strength.good {
            background: #f59e0b;
            width: 75%;
        }
        
        .password-strength.strong {
            background: var(--success-color);
            width: 100%;
        }
        
        .security-tips {
            background: var(--bg-light);
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            border-radius: 0 8px 8px 0;
        }
        
        .security-tips ul {
            margin-bottom: 0;
        }
        
        .security-tips li {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews Admin
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-shield me-2"></i><?php echo htmlspecialchars($user['name']); ?>
                </span>
                <a class="nav-link" href="/logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
<div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <div>
                                <div class="stats-number"><?php echo $pending_reviews_count; ?></div>
                                <div>Pending Reviews</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                            <div>
                                <div class="stats-number"><?php echo $pending_withdrawals_count; ?></div>
                                <div>Pending Withdrawals</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #dc2626);">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-ban fa-2x"></i>
                            </div>
                            <div>
                                <div class="stats-number"><?php echo $banned_users_count; ?></div>
                                <div>Banned Users</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #059669);">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                            <div>
                                <div class="stats-number">$<?php echo number_format($dashboard_stats['total_revenue'], 2); ?></div>
                                <div>Total Revenue</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Analytics Stats -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;">$<?php echo number_format($dashboard_stats['today_revenue'], 2); ?></div>
                        <div style="font-size: 0.875rem;">Today's Revenue</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #06b6d4, #0891b2); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;"><?php echo $dashboard_stats['total_users']; ?></div>
                        <div style="font-size: 0.875rem;">Total Users</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #10b981, #059669); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;"><?php echo $dashboard_stats['new_users_today']; ?></div>
                        <div style="font-size: 0.875rem;">New Users Today</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b, #d97706); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;"><?php echo $dashboard_stats['total_reviews']; ?></div>
                        <div style="font-size: 0.875rem;">Total Reviews</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #ef4444, #dc2626); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;"><?php echo $dashboard_stats['reviews_today']; ?></div>
                        <div style="font-size: 0.875rem;">Reviews Today</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stats-card" style="background: linear-gradient(135deg, #84cc16, #65a30d); padding: 1rem;">
                        <div class="stats-number" style="font-size: 1.5rem;"><?php echo $dashboard_stats['completion_rate']; ?>%</div>
                        <div style="font-size: 0.875rem;">Completion Rate</div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="card mb-4">
                <div class="card-body p-0">
                    <ul class="nav nav-pills p-3" id="adminTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#analytics" type="button">
                                <i class="fas fa-chart-bar me-2"></i>Analytics Dashboard
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#reviews" type="button">
                                <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#users" type="button">
                                <i class="fas fa-users me-2"></i>User Management
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#withdrawals" type="button">
                                <i class="fas fa-money-bill-wave me-2"></i>Withdrawals
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#storage" type="button">
                                <i class="fas fa-hdd me-2"></i>Storage Management
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#services" type="button">
                                <i class="fas fa-cogs me-2"></i>Services Management
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#currency" type="button">
                                <i class="fas fa-coins me-2"></i>Currency Settings
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="pill" data-bs-target="#passwords" type="button">
                                <i class="fas fa-key me-2"></i>Password Management
                            </button>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="tab-content" id="adminTabContent">
                <!-- Analytics Dashboard Tab -->
                <div class="tab-pane fade show active" id="analytics">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Revenue Analytics (Last 30 Days)</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="revenueChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>User Growth (Last 30 Days)</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="userGrowthChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Platform Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="platformChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="activity-timeline">
                                        <?php
                                        // Get recent activity
                                        $stmt = $pdo->prepare('
                                            SELECT "review" as type, r.created_at, r.amount, u.name as user_name, r.status
                                            FROM reviews r 
                                            JOIN users u ON r.reviewer_id = u.id 
                                            WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                                            UNION ALL
                                            SELECT "withdrawal" as type, w.created_at, w.amount, u.name as user_name, w.status
                                            FROM withdrawals w 
                                            JOIN users u ON w.user_id = u.id 
                                            WHERE w.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                                            ORDER BY created_at DESC 
                                            LIMIT 10
                                        ');
                                        $stmt->execute();
                                        $recent_activity = $stmt->fetchAll();
                                        ?>
                                        
                                        <?php if (empty($recent_activity)): ?>
                                            <div class="text-center py-3">
                                                <i class="fas fa-info-circle text-muted"></i>
                                                <p class="text-muted mb-0">No recent activity</p>
                                            </div>
                                        <?php else: ?>
                                            <?php foreach ($recent_activity as $activity): ?>
                                                <div class="activity-item d-flex align-items-center mb-3">
                                                    <div class="activity-icon me-3">
                                                        <?php if ($activity['type'] === 'review'): ?>
                                                            <i class="fas fa-star text-warning"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-money-bill-wave text-success"></i>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="activity-content flex-grow-1">
                                                        <div class="fw-bold">
                                                            <?php echo htmlspecialchars($activity['user_name']); ?>
                                                            <?php if ($activity['type'] === 'review'): ?>
                                                                submitted a review
                                                            <?php else: ?>
                                                                requested withdrawal
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="text-muted small">
                                                            $<?php echo number_format($activity['amount'], 2); ?> • 
                                                            <?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?>
                                                        </div>
                                                    </div>
                                                    <div class="activity-status">
                                                        <span class="badge bg-<?php echo $activity['status'] === 'approved' ? 'success' : ($activity['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                            <?php echo ucfirst($activity['status']); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Reviews Tab -->
                <div class="tab-pane fade" id="reviews">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Pending Review Approvals</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($pending_reviews)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h5>No pending reviews</h5>
                                    <p class="text-muted">All reviews have been processed</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($pending_reviews as $review): ?>
                                    <div class="review-card">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="platform-badge platform-<?php echo strtolower($review['platform']); ?>">
                                                <i class="fab fa-<?php echo strtolower($review['platform']); ?> me-2"></i>
                                                <?php echo htmlspecialchars($review['platform']); ?>
                                            </div>
                                            <div class="amount-badge">
                                                $<?php echo number_format($review['amount'], 2); ?>
                                            </div>
                                        </div>
                                        
                                        <h6 class="mb-2"><?php echo htmlspecialchars($review['business_name']); ?></h6>
                                        <p class="text-muted mb-3">
                                            <i class="fas fa-user me-2"></i>Customer: <?php echo htmlspecialchars($review['customer_name']); ?>
                                            <span class="ms-3"><i class="fas fa-user-check me-2"></i>Reviewer: <?php echo htmlspecialchars($review['reviewer_name']); ?></span>
                                        </p>
                                        
                                        <div class="mb-3">
                                            <strong>Review Text:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?php echo nl2br(htmlspecialchars($review['review_text'])); ?>
                                            </div>
                                        </div>
                                        
                                        <?php if ($review['screenshot_url']): ?>
                                            <div class="mb-3">
                                                <strong>Review Screenshot:</strong>
                                                <div class="mt-2">
                                                    <img src="<?php echo htmlspecialchars($review['screenshot_url']); ?>" class="screenshot-preview" alt="Review Screenshot">
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-success" onclick="approveReview(<?php echo $review['id']; ?>)">
                                                <i class="fas fa-check me-2"></i>Approve
                                            </button>
                                            <button class="btn btn-danger" onclick="rejectReview(<?php echo $review['id']; ?>)">
                                                <i class="fas fa-times me-2"></i>Reject
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- User Management Tab -->
                <div class="tab-pane fade" id="users">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-users me-2"></i>User Management</h5>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                <i class="fas fa-plus me-2"></i>Create User
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Reviews</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $u): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($u['name']); ?></strong>
                                                        <div class="text-muted small"><?php echo htmlspecialchars($u['email']); ?></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="role-badge role-<?php echo $u['role']; ?>">
                                                        <?php echo ucfirst($u['role']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $u['status'] ?? 'active'; ?>">
                                                        <?php echo ucfirst($u['status'] ?? 'active'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="small">
                                                        <div>Total: <?php echo $u['total_reviews']; ?></div>
                                                        <div class="text-success">Approved: <?php echo $u['approved_reviews']; ?></div>
                                                        <div class="text-danger">Rejected: <?php echo $u['rejected_reviews']; ?></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="small">
                                                        <?php echo date('M j, Y', strtotime($u['created_at'])); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-1">
                                                        <?php if (($u['status'] ?? 'active') === 'active'): ?>
                                                            <button class="btn btn-warning btn-sm" onclick="banUser(<?php echo $u['id']; ?>, '<?php echo addslashes($u['name']); ?>')">
                                                                <i class="fas fa-ban me-1"></i>Ban
                                                            </button>
                                                        <?php else: ?>
                                                            <button class="btn btn-success btn-sm" onclick="unbanUser(<?php echo $u['id']; ?>, '<?php echo addslashes($u['name']); ?>')">
                                                                <i class="fas fa-check me-1"></i>Unban
                                                            </button>
                                                        <?php endif; ?>
                                                        <button class="btn btn-info btn-sm" onclick="resetUserPassword(<?php echo $u['id']; ?>, '<?php echo addslashes($u['name']); ?>')">
                                                            <i class="fas fa-key me-1"></i>Reset Password
                                                        </button>
                                                        <button class="btn btn-danger btn-sm" onclick="deleteUser(<?php echo $u['id']; ?>, '<?php echo addslashes($u['name']); ?>')">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawals Tab -->
                <div class="tab-pane fade" id="withdrawals">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Pending Withdrawals</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($pending_withdrawals)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h5>No pending withdrawals</h5>
                                    <p class="text-muted">All withdrawal requests have been processed</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($pending_withdrawals as $withdrawal): ?>
                                    <div class="withdrawal-card">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($withdrawal['user_name']); ?></h6>
                                                <p class="text-muted mb-0"><?php echo htmlspecialchars($withdrawal['user_email']); ?></p>
                                            </div>
                                            <div class="amount-badge">
                                                $<?php echo number_format($withdrawal['amount'], 2); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <strong>UPI ID:</strong> <?php echo htmlspecialchars($withdrawal['upi_id']); ?>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>WhatsApp:</strong> <?php echo htmlspecialchars($withdrawal['whatsapp']); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-success" onclick="approveWithdrawal(<?php echo $withdrawal['id']; ?>)">
                                                <i class="fas fa-check me-2"></i>Approve
                                            </button>
                                            <button class="btn btn-danger" onclick="rejectWithdrawal(<?php echo $withdrawal['id']; ?>)">
                                                <i class="fas fa-times me-2"></i>Reject
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Storage Management Tab -->
                <div class="tab-pane fade" id="storage">
                    <div class="row">
                        <!-- Storage Statistics -->
                        <div class="col-lg-4">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Storage Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6 mb-3">
                                            <div class="stats-card" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); padding: 1rem;">
                                                <div class="stats-number" style="font-size: 1.5rem;"><?php echo $storage_stats['total_images']; ?></div>
                                                <div style="font-size: 0.875rem;">Total Images</div>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="stats-card" style="background: linear-gradient(135deg, var(--warning-color), #d97706); padding: 1rem;">
                                                <div class="stats-number" style="font-size: 1.5rem;"><?php echo $storage_stats['pending_images']; ?></div>
                                                <div style="font-size: 0.875rem;">Pending Images</div>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="stats-card" style="background: linear-gradient(135deg, var(--danger-color), #dc2626); padding: 1rem;">
                                                <div class="stats-number" style="font-size: 1.5rem;"><?php echo $storage_stats['old_images']; ?></div>
                                                <div style="font-size: 0.875rem;">Old Images</div>
                                            </div>
                                        </div>
                                        <div class="col-6 mb-3">
                                            <div class="stats-card" style="background: linear-gradient(135deg, var(--success-color), #059669); padding: 1rem;">
                                                <div class="stats-number" style="font-size: 1.5rem;"><?php echo formatBytes($storage_stats['total_size']); ?></div>
                                                <div style="font-size: 0.875rem;">Total Size</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cleanup Options -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-broom me-2"></i>Storage Cleanup</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Automatic Cleanup:</strong> Images from approved/rejected reviews are automatically deleted after 24 hours to save storage space.
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border-warning">
                                                <div class="card-body">
                                                    <h6 class="card-title text-warning">
                                                        <i class="fas fa-clock me-2"></i>Clean Old Images
                                                    </h6>
                                                    <p class="card-text small">Delete images from approved/rejected reviews older than 24 hours.</p>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="cleanup_type" value="old_reviews">
                                                        <button type="submit" name="cleanup_storage" class="btn btn-warning btn-sm" onclick="return confirm('Delete old review images? This will free up storage space.')">
                                                            <i class="fas fa-trash me-2"></i>Clean Old Images
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="card border-danger">
                                                <div class="card-body">
                                                    <h6 class="card-title text-danger">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>Clean All Images
                                                    </h6>
                                                    <p class="card-text small">Delete ALL review images. Use with caution - this cannot be undone!</p>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="cleanup_type" value="all_images">
                                                        <button type="submit" name="cleanup_storage" class="btn btn-danger btn-sm" onclick="return confirm('WARNING: This will delete ALL review images! Are you sure?')">
                                                            <i class="fas fa-trash-alt me-2"></i>Clean All Images
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <h6><i class="fas fa-cog me-2"></i>Storage Settings</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoCleanup" checked disabled>
                                                    <label class="form-check-label" for="autoCleanup">
                                                        Auto-cleanup after 24 hours
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="compressImages" checked disabled>
                                                    <label class="form-check-label" for="compressImages">
                                                        Compress images to WebP
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Management Tab -->
                <div class="tab-pane fade" id="services">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Services Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Service</th>
                                            <th>Min Price</th>
                                            <th>Max Price</th>
                                            <th>Default Price</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($services as $service): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($service['display_name']); ?></strong>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($service['min_price']); ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($service['max_price']); ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($service['default_price']); ?>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $service['is_active'] ? 'active' : 'banned'; ?>">
                                                        <?php echo $service['is_active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <button class="btn btn-primary btn-sm" onclick="updateService(<?php echo $service['id']; ?>, '<?php echo addslashes($service['display_name']); ?>')">
                                                            <i class="fas fa-edit me-2"></i>Edit
                                                        </button>
                                                        <button class="btn btn-warning btn-sm" onclick="toggleServiceStatus(<?php echo $service['id']; ?>, '<?php echo addslashes($service['display_name']); ?>')">
                                                            <i class="fas fa-toggle-on me-2"></i>Toggle Status
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Currency Settings Tab -->
                <div class="tab-pane fade" id="currency">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-coins me-2"></i>Currency & Wallet Settings</h5>
                        </div>
                        <div class="card-body">
                            <?php 
                            $currency_settings = get_currency_settings(); 
                            $currency_options = get_currency_options();
                            ?>
                            <form method="post">
                                <input type="hidden" name="update_currency_settings" value="1">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">
                                                <i class="fas fa-globe me-2"></i>Platform Currency
                                            </label>
                                            <select name="currency" class="form-select" required onchange="updateCurrencySymbol(this.value)">
                                                <?php foreach ($currency_options as $code => $details): ?>
                                                    <option value="<?php echo $code; ?>" <?php echo ($currency_settings['currency'] === $code) ? 'selected' : ''; ?>>
                                                        <?php echo $details['symbol']; ?> - <?php echo $details['name']; ?> (<?php echo $code; ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Choose the primary currency for your platform</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">
                                                <i class="fas fa-dollar-sign me-2"></i>Currency Symbol
                                            </label>
                                            <input type="text" name="currency_symbol" class="form-control" 
                                                   value="<?php echo htmlspecialchars($currency_settings['symbol']); ?>" 
                                                   required maxlength="5" id="currencySymbol">
                                            <div class="form-text">Symbol displayed with amounts (e.g., $, €, ₹)</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6 class="mb-3"><i class="fas fa-gift me-2"></i>Signup Bonuses</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">Customer Signup Bonus</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo htmlspecialchars($currency_settings['symbol']); ?></span>
                                                <input type="number" name="signup_bonus_customer" class="form-control" 
                                                       value="<?php echo $currency_settings['signup_bonus_customer']; ?>" 
                                                       min="0" step="0.01" required>
                                            </div>
                                            <div class="form-text">Bonus given to new customers</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">Reviewer Signup Bonus</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo htmlspecialchars($currency_settings['symbol']); ?></span>
                                                <input type="number" name="signup_bonus_reviewer" class="form-control" 
                                                       value="<?php echo $currency_settings['signup_bonus_reviewer']; ?>" 
                                                       min="0" step="0.01" required>
                                            </div>
                                            <div class="form-text">Bonus given to new reviewers</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>Platform Settings</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">Minimum Payout Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo htmlspecialchars($currency_settings['symbol']); ?></span>
                                                <input type="number" name="minimum_payout" class="form-control" 
                                                       value="<?php echo $currency_settings['minimum_payout']; ?>" 
                                                       min="0.01" step="0.01" required>
                                            </div>
                                            <div class="form-text">Minimum amount reviewers can withdraw</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">Platform Fee Percentage</label>
                                            <div class="input-group">
                                                <input type="number" name="platform_fee_percentage" class="form-control" 
                                                       value="<?php echo $currency_settings['platform_fee_percentage']; ?>" 
                                                       min="0" max="100" step="0.01" required>
                                                <span class="input-group-text">%</span>
                                            </div>
                                            <div class="form-text">Platform fee on review payments</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Current Settings Summary</h6>
                                    <ul class="mb-0">
                                        <li>Currency: <strong><?php echo $currency_settings['currency']; ?> (<?php echo $currency_settings['symbol']; ?>)</strong></li>
                                        <li>Customer Bonus: <strong><?php echo format_currency($currency_settings['signup_bonus_customer']); ?></strong></li>
                                        <li>Reviewer Bonus: <strong><?php echo format_currency($currency_settings['signup_bonus_reviewer']); ?></strong></li>
                                        <li>Minimum Payout: <strong><?php echo format_currency($currency_settings['minimum_payout']); ?></strong></li>
                                        <li>Platform Fee: <strong><?php echo $currency_settings['platform_fee_percentage']; ?>%</strong></li>
                                    </ul>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save me-2"></i>Save Currency Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Password Management Tab -->
                <div class="tab-pane fade" id="passwords">
                    <div class="row">
                        <!-- Admin Password Change -->
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>Change Admin Password</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        <input type="hidden" name="change_admin_password" value="1">
                                        
                                        <div class="mb-3">
                                            <label for="currentPassword" class="form-label">Current Password *</label>
                                            <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                                            <div class="form-text">Enter your current admin password</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="newAdminPassword" class="form-label">New Password *</label>
                                            <input type="password" class="form-control" id="newAdminPassword" name="new_password" required minlength="6">
                                            <div class="form-text">Minimum 6 characters</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="confirmAdminPassword" class="form-label">Confirm New Password *</label>
                                            <input type="password" class="form-control" id="confirmAdminPassword" name="confirm_password" required minlength="6">
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Security Note:</strong> Choose a strong password with a mix of letters, numbers, and special characters.
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-key me-2"></i>Change Admin Password
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Password Management -->
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>User Password Management</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Important:</strong> Use the "Reset Password" button in the User Management tab to reset individual user passwords.
                                    </div>
                                    
                                    <!-- Admins Without Passwords -->
                                    <?php
                                    // Get admins without passwords (for initial setup)
                                    $stmt = $pdo->prepare('SELECT id, name, email FROM users WHERE role = "admin" AND (password IS NULL OR password = "")');
                                    $stmt->execute();
                                    $admins_without_password = $stmt->fetchAll();
                                    ?>
                                    
                                    <?php if (!empty($admins_without_password)): ?>
                                        <div class="mb-4">
                                            <h6 class="text-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>Admins Without Passwords
                                            </h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Admin</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($admins_without_password as $admin): ?>
                                                            <tr>
                                                                <td>
                                                                    <div>
                                                                        <strong><?php echo htmlspecialchars($admin['name']); ?></strong>
                                                                        <div class="text-muted small"><?php echo htmlspecialchars($admin['email']); ?></div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <button class="btn btn-warning btn-sm" onclick="setInitialPassword(<?php echo $admin['id']; ?>, '<?php echo addslashes($admin['name']); ?>')">
                                                                        <i class="fas fa-key me-1"></i>Set Password
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h6 class="mb-3"><i class="fas fa-list me-2"></i>Recent Password Resets</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>Role</th>
                                                    <th>Reset Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                // Get recent password reset activity (you can add a password_reset_logs table later)
                                                $stmt = $pdo->prepare('
                                                    SELECT u.name, u.email, u.role, u.updated_at 
                                                    FROM users u 
                                                    WHERE u.updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                                                    ORDER BY u.updated_at DESC 
                                                    LIMIT 5
                                                ');
                                                $stmt->execute();
                                                $recent_updates = $stmt->fetchAll();
                                                ?>
                                                
                                                <?php if (empty($recent_updates)): ?>
                                                    <tr>
                                                        <td colspan="3" class="text-center text-muted">
                                                            <i class="fas fa-info-circle me-2"></i>No recent password changes
                                                        </td>
                                                    </tr>
                                                <?php else: ?>
                                                    <?php foreach ($recent_updates as $update): ?>
                                                        <tr>
                                                            <td>
                                                                <div>
                                                                    <strong><?php echo htmlspecialchars($update['name']); ?></strong>
                                                                    <div class="text-muted small"><?php echo htmlspecialchars($update['email']); ?></div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <span class="role-badge role-<?php echo $update['role']; ?>">
                                                                    <?php echo ucfirst($update['role']); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <div class="small">
                                                                    <?php echo date('M j, g:i A', strtotime($update['updated_at'])); ?>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <h6><i class="fas fa-shield-alt me-2"></i>Password Security Tips</h6>
                                        <ul class="small text-muted">
                                            <li>Use at least 8 characters for admin passwords</li>
                                            <li>Include uppercase, lowercase, numbers, and symbols</li>
                                            <li>Never share passwords or write them down</li>
                                            <li>Change passwords regularly</li>
                                            <li>Use different passwords for different accounts</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Action Modal -->
    <div class="modal fade" id="reviewActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewActionTitle">Review Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="review_id" id="reviewActionId">
                        <input type="hidden" name="action" id="reviewActionType">
                        
                        <div id="rejectionReasonDiv" style="display: none;">
                            <label class="form-label">Rejection Reason *</label>
                            <select name="rejection_reason" class="form-select" required>
                                <option value="">Select a reason...</option>
                                <option value="Wrong review content">Wrong review content</option>
                                <option value="Poor quality screenshot">Poor quality screenshot</option>
                                <option value="Review not posted">Review not posted</option>
                                <option value="Fake screenshot">Fake screenshot</option>
                                <option value="Inappropriate content">Inappropriate content</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="review_action" class="btn btn-primary">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- User Action Modal -->
    <div class="modal fade" id="userActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userActionTitle">User Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="user_id" id="userActionId">
                        <input type="hidden" name="action" id="userActionType">
                        
                        <div id="banReasonDiv" style="display: none;">
                            <label class="form-label">Ban Reason *</label>
                            <select name="ban_reason" class="form-select" required>
                                <option value="">Select a reason...</option>
                                <option value="Spam reviews">Spam reviews</option>
                                <option value="Fake screenshots">Fake screenshots</option>
                                <option value="Inappropriate behavior">Inappropriate behavior</option>
                                <option value="Multiple violations">Multiple violations</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="user_action" class="btn btn-primary">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Withdrawal Action Modal -->
    <div class="modal fade" id="withdrawalActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawalActionTitle">Withdrawal Action</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="withdrawal_id" id="withdrawalActionId">
                        <input type="hidden" name="action" id="withdrawalActionType">
                        
                        <div id="withdrawalRejectionReasonDiv" style="display: none;">
                            <label class="form-label">Rejection Reason *</label>
                            <select name="rejection_reason" class="form-select" required>
                                <option value="">Select a reason...</option>
                                <option value="Invalid UPI ID">Invalid UPI ID</option>
                                <option value="Insufficient balance">Insufficient balance</option>
                                <option value="Suspicious activity">Suspicious activity</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="withdrawal_action" class="btn btn-primary">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Service Management Modal -->
    <div class="modal fade" id="serviceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="serviceModalTitle">Edit Service</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="service_action" value="1">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="service_id" id="serviceId">
                        
                        <div class="mb-3">
                            <label for="serviceName" class="form-label">Service Name</label>
                            <input type="text" class="form-control" id="serviceName" readonly>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="minPrice" class="form-label">Minimum Price ($)</label>
                                    <input type="number" class="form-control" id="minPrice" name="min_price" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="maxPrice" class="form-label">Maximum Price ($)</label>
                                    <input type="number" class="form-control" id="maxPrice" name="max_price" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="defaultPrice" class="form-label">Default Price ($)</label>
                                    <input type="number" class="form-control" id="defaultPrice" name="default_price" step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    Service is active
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Service</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="create_user" value="1">
                        
                        <div class="mb-3">
                            <label for="userName" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="userName" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="userEmail" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="userPassword" name="password" required minlength="6">
                        </div>
                        
                        <div class="mb-3">
                            <label for="userRole" class="form-label">Role *</label>
                            <select class="form-select" id="userRole" name="role" required>
                                <option value="">Select role...</option>
                                <option value="customer">Customer</option>
                                <option value="reviewer">Reviewer</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userWhatsapp" class="form-label">WhatsApp Number</label>
                            <input type="text" class="form-control" id="userWhatsapp" name="whatsapp" placeholder="+1234567890">
                        </div>
                        
                        <div class="mb-3">
                            <label for="userUpi" class="form-label">UPI ID</label>
                            <input type="text" class="form-control" id="userUpi" name="upi_id" placeholder="user@upi">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Delete User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="delete_user" value="1">
                        <input type="hidden" name="user_id" id="deleteUserId">
                        
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone. The user and all their data will be permanently deleted.
                        </div>
                        
                        <p>Are you sure you want to delete <strong id="deleteUserName"></strong>?</p>
                        
                        <div class="mb-3">
                            <label for="deleteConfirmation" class="form-label">Type "DELETE" to confirm *</label>
                            <input type="text" class="form-control" id="deleteConfirmation" name="confirmation" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset User Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reset User Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="reset_user_password" value="1">
                        <input type="hidden" name="user_id" id="resetPasswordUserId">
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Password Reset:</strong> This will reset the password for <strong id="resetPasswordUserName"></strong>
                        </div>
                        
                        <div class="mb-3">
                            <label for="newUserPassword" class="form-label">New Password *</label>
                            <input type="password" class="form-control" id="newUserPassword" name="new_password" required minlength="6">
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmUserPassword" class="form-label">Confirm New Password *</label>
                            <input type="password" class="form-control" id="confirmUserPassword" name="confirm_password" required minlength="6">
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> The user will need to use this new password to log in. Consider notifying them about the password change.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Set Initial Password Modal -->
    <div class="modal fade" id="setInitialPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Set Initial Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="set_initial_password" value="1">
                        <input type="hidden" name="admin_id" id="setInitialPasswordAdminId">
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Set Password:</strong> This will set the initial password for <strong id="setInitialPasswordAdminName"></strong>
                        </div>
                        
                        <div class="mb-3">
                            <label for="initialPassword" class="form-label">New Password *</label>
                            <input type="password" class="form-control" id="initialPassword" name="new_password" required minlength="6">
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmInitialPassword" class="form-label">Confirm New Password *</label>
                            <input type="password" class="form-control" id="confirmInitialPassword" name="confirm_password" required minlength="6">
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> This admin will be able to log in immediately with this password.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Set Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Review actions
        function approveReview(reviewId) {
            if (confirm('Are you sure you want to approve this review? The reviewer will receive 80% of the amount.')) {
                document.getElementById('reviewActionId').value = reviewId;
                document.getElementById('reviewActionType').value = 'approve';
                document.getElementById('reviewActionTitle').textContent = 'Approve Review';
                document.getElementById('rejectionReasonDiv').style.display = 'none';
                new bootstrap.Modal(document.getElementById('reviewActionModal')).show();
            }
        }
        
        function rejectReview(reviewId) {
            document.getElementById('reviewActionId').value = reviewId;
            document.getElementById('reviewActionType').value = 'reject';
            document.getElementById('reviewActionTitle').textContent = 'Reject Review';
            document.getElementById('rejectionReasonDiv').style.display = 'block';
            new bootstrap.Modal(document.getElementById('reviewActionModal')).show();
        }
        
        // User actions
        function banUser(userId, userName) {
            if (confirm(`Are you sure you want to ban ${userName}?`)) {
                document.getElementById('userActionId').value = userId;
                document.getElementById('userActionType').value = 'ban';
                document.getElementById('userActionTitle').textContent = 'Ban User';
                document.getElementById('banReasonDiv').style.display = 'block';
                new bootstrap.Modal(document.getElementById('userActionModal')).show();
            }
        }
        
        function unbanUser(userId, userName) {
            if (confirm(`Are you sure you want to unban ${userName}?`)) {
                document.getElementById('userActionId').value = userId;
                document.getElementById('userActionType').value = 'unban';
                document.getElementById('userActionTitle').textContent = 'Unban User';
                document.getElementById('banReasonDiv').style.display = 'none';
                new bootstrap.Modal(document.getElementById('userActionModal')).show();
            }
        }
        
        // Withdrawal actions
        function approveWithdrawal(withdrawalId) {
            if (confirm('Are you sure you want to approve this withdrawal request?')) {
                document.getElementById('withdrawalActionId').value = withdrawalId;
                document.getElementById('withdrawalActionType').value = 'approve';
                document.getElementById('withdrawalActionTitle').textContent = 'Approve Withdrawal';
                document.getElementById('withdrawalRejectionReasonDiv').style.display = 'none';
                new bootstrap.Modal(document.getElementById('withdrawalActionModal')).show();
            }
        }
        
        function rejectWithdrawal(withdrawalId) {
            document.getElementById('withdrawalActionId').value = withdrawalId;
            document.getElementById('withdrawalActionType').value = 'reject';
            document.getElementById('withdrawalActionTitle').textContent = 'Reject Withdrawal';
            document.getElementById('withdrawalRejectionReasonDiv').style.display = 'block';
            new bootstrap.Modal(document.getElementById('withdrawalActionModal')).show();
        }

        function updateService(serviceId, serviceName) {
            // Fetch service data via AJAX
            fetch(`get_service_data.php?service_id=${serviceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }
                    
                    document.getElementById('serviceId').value = serviceId;
                    document.getElementById('serviceModalTitle').textContent = 'Edit ' + serviceName;
                    document.getElementById('serviceName').value = data.display_name;
                    document.getElementById('minPrice').value = data.min_price;
                    document.getElementById('maxPrice').value = data.max_price;
                    document.getElementById('defaultPrice').value = data.default_price;
                    document.getElementById('isActive').checked = data.is_active == 1;
                    
                    new bootstrap.Modal(document.getElementById('serviceModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error fetching service data');
                });
        }
        
        function toggleServiceStatus(serviceId, serviceName) {
            if (confirm('Are you sure you want to toggle the status for ' + serviceName + '?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="service_action" value="1">
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="service_id" value="${serviceId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Delete user function
        function deleteUser(userId, userName) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserName').textContent = userName;
            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
        
        // Reset user password function
        function resetUserPassword(userId, userName) {
            document.getElementById('resetPasswordUserId').value = userId;
            document.getElementById('resetPasswordUserName').textContent = userName;
            new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
        }
        
        // Currency settings
        function updateCurrencySymbol(currency) {
            const currencyMap = <?php echo json_encode(get_currency_options()); ?>;
            const symbolInput = document.getElementById('currencySymbol');
            if (currencyMap[currency] && symbolInput) {
                symbolInput.value = currencyMap[currency].symbol;
            }
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart');
            if (revenueCtx) {
                const revenueData = <?php echo json_encode($revenue_data); ?>;
                const labels = revenueData.map(item => item.date);
                const data = revenueData.map(item => parseFloat(item.revenue));
                
                new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Revenue ($)',
                            data: data,
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toFixed(2);
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // User Growth Chart
            const userGrowthCtx = document.getElementById('userGrowthChart');
            if (userGrowthCtx) {
                const userGrowthData = <?php echo json_encode($user_growth_data); ?>;
                const labels = userGrowthData.map(item => item.date);
                const data = userGrowthData.map(item => parseInt(item.new_users));
                
                new Chart(userGrowthCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'New Users',
                            data: data,
                            backgroundColor: '#10b981',
                            borderColor: '#059669',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }

            // Platform Distribution Chart
            const platformCtx = document.getElementById('platformChart');
            if (platformCtx) {
                <?php
                // Get platform distribution data
                $stmt = $pdo->prepare('
                    SELECT platform, COUNT(*) as count 
                    FROM reviews 
                    WHERE status = "approved" 
                    GROUP BY platform 
                    ORDER BY count DESC
                ');
                $stmt->execute();
                $platform_data = $stmt->fetchAll();
                ?>
                
                const platformData = <?php echo json_encode($platform_data); ?>;
                const platformLabels = platformData.map(item => item.platform);
                const platformCounts = platformData.map(item => parseInt(item.count));
                const platformColors = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#6b7280'];
                
                new Chart(platformCtx, {
                    type: 'doughnut',
                    data: {
                        labels: platformLabels,
                        datasets: [{
                            data: platformCounts,
                            backgroundColor: platformColors,
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        });

        function setInitialPassword(adminId, adminName) {
            document.getElementById('setInitialPasswordAdminId').value = adminId;
            document.getElementById('setInitialPasswordAdminName').textContent = adminName;
            new bootstrap.Modal(document.getElementById('setInitialPasswordModal')).show();
        }

        function changeAdminPassword() {
            new bootstrap.Modal(document.getElementById('changeAdminPasswordModal')).show();
        }
    </script>
</body>
</html>

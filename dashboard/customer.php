<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'customer') {
    header('Location: /index.php');
    exit;
}
require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle balance top-up
if (isset($_POST['add_balance'])) {
    $amount = floatval($_POST['balance_amount']);
    if ($amount > 0) {
        $pdo->beginTransaction();
        try {
            $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?');
            $stmt->execute([$amount, $user['id']]);
            $stmt = $pdo->prepare('INSERT INTO wallet_transactions (user_id, type, amount, reference) VALUES (?, "topup", ?, ?)');
            $stmt->execute([$user['id'], $amount, 'Balance top-up']);
            $pdo->commit();
            set_flash('<div class="alert alert-success">Balance added successfully!</div>');
            $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
            $stmt->execute([$user['id']]);
            $user = $stmt->fetch();
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error adding balance. Please try again.</div>');
        }
    }
    header('Location: customer.php');
    exit;
}

// Handle review post
if (isset($_POST['post_reviews'])) {
    $platform = trim($_POST['platform'] ?? '');
    $business_name = trim($_POST['business_name'] ?? '');
    $business_link = trim($_POST['business_link'] ?? '');
    $country = trim($_POST['country'] ?? '');
    $total_amount = 0;
    $reviews_data = [];
    
    // Collect all reviews
    for ($i = 0; $i < 10; $i++) {
        if (!empty($_POST['review_text_' . $i])) {
            $reviews_data[] = [
                'text' => trim($_POST['review_text_' . $i]),
                'amount' => floatval($_POST['review_amount_' . $i])
            ];
            $total_amount += floatval($_POST['review_amount_' . $i]);
        }
    }
    
    // Handle screenshot upload
    $screenshot_url = null;
    if (!empty($_FILES['screenshot']['name'])) {
        $target_dir = __DIR__ . '/../uploads/';
        if (!is_dir($target_dir)) mkdir($target_dir, 0777, true);
        $filename = uniqid('ss_') . '_' . basename($_FILES['screenshot']['name']);
        $target_file = $target_dir . $filename;
        if (move_uploaded_file($_FILES['screenshot']['tmp_name'], $target_file)) {
            $screenshot_url = '/uploads/' . $filename;
        }
    }
    
    if ($platform && $business_name && !empty($reviews_data) && $screenshot_url) {
        // Validate minimum prices against service settings
        $stmt = $pdo->prepare('SELECT min_price, max_price FROM services WHERE platform_name = ? AND is_active = 1');
        $stmt->execute([$platform]);
        $service = $stmt->fetch();
        
        if ($service) {
            $validation_errors = [];
            foreach ($reviews_data as $index => $review) {
                if ($review['amount'] < $service['min_price']) {
                    $validation_errors[] = "Review " . ($index + 1) . ": Amount must be at least $" . number_format($service['min_price'], 2) . " for " . ucfirst($platform) . " reviews";
                }
                if ($review['amount'] > $service['max_price']) {
                    $validation_errors[] = "Review " . ($index + 1) . ": Amount cannot exceed $" . number_format($service['max_price'], 2) . " for " . ucfirst($platform) . " reviews";
                }
            }
            
            if (!empty($validation_errors)) {
                set_flash('<div class="alert alert-danger"><strong>Price Validation Errors:</strong><br>' . implode('<br>', $validation_errors) . '</div>');
                header('Location: customer.php');
                exit;
            }
        }
        
        if ($user['wallet_balance'] < $total_amount) {
            set_flash('<div class="alert alert-danger">Insufficient wallet balance. You need $' . number_format($total_amount, 2) . ' but have $' . number_format($user['wallet_balance'], 2) . '</div>');
        } else {
            $pdo->beginTransaction();
            try {
                // Deduct total amount
                $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?');
                $stmt->execute([$total_amount, $user['id']]);
                
                // Create reviews
                foreach ($reviews_data as $review) {
                    $stmt = $pdo->prepare('INSERT INTO reviews (customer_id, platform, business_name, business_link, country, review_text, amount, status, screenshot_url) VALUES (?, ?, ?, ?, ?, ?, ?, "open", ?)');
                    $stmt->execute([$user['id'], $platform, $business_name, $business_link, $country, $review['text'], $review['amount'], $screenshot_url]);
                    $review_id = $pdo->lastInsertId();
                    
                    // Record transaction
                    $stmt = $pdo->prepare('INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference) VALUES (?, "debit", ?, ?, ?)');
                    $stmt->execute([$user['id'], $review['amount'], $review_id, 'Review posted']);
                }
                
                $pdo->commit();
                set_flash('<div class="alert alert-success">' . count($reviews_data) . ' review(s) posted successfully! Total amount: $' . number_format($total_amount, 2) . '</div>');
                $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
                $stmt->execute([$user['id']]);
                $user = $stmt->fetch();
            } catch (Exception $e) {
                $pdo->rollBack();
                error_log('Review posting error: ' . $e->getMessage());
                set_flash('<div class="alert alert-danger">Error posting reviews: ' . $e->getMessage() . '</div>');
            }
        }
    } else {
        $errors = [];
        if (!$platform) $errors[] = 'Platform is required';
        if (!$business_name) $errors[] = 'Business name is required';
        if (empty($reviews_data)) $errors[] = 'At least one review is required';
        if (!$screenshot_url) $errors[] = 'Business profile screenshot is mandatory';
        set_flash('<div class="alert alert-danger">' . implode(', ', $errors) . '</div>');
    }
    header('Location: customer.php');
    exit;
}

// Fetch previous reviews
$stmt = $pdo->prepare('SELECT * FROM reviews WHERE customer_id = ? ORDER BY created_at DESC');
$stmt->execute([$user['id']]);
$reviews = $stmt->fetchAll();
$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/6.6.6/css/flag-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .btn-success {
            background: var(--success-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        
        .wallet-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .wallet-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .platform-option {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .platform-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .platform-option.selected {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .platform-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        
        .platform-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }
        
        .platform-desc {
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        .country-flag {
            width: 40px;
            height: 30px;
            border-radius: 4px;
            margin-right: 0.5rem;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .country-flag:hover {
            transform: scale(1.1);
        }
        
        .country-flag.selected {
            border: 3px solid var(--primary-color);
        }
        
        .review-item {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            position: relative;
        }
        
        .review-item:hover {
            border-color: var(--primary-color);
        }
        
        .remove-review {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .remove-review:hover {
            background: #dc2626;
            transform: scale(1.1);
        }
        
        .add-review-btn {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .add-review-btn:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .screenshot-upload {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: white;
        }
        
        .screenshot-upload:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .screenshot-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-open { background: #fef3c7; color: #92400e; }
        .status-claimed { background: #dbeafe; color: #1e40af; }
        .status-submitted { background: #d1fae5; color: #065f46; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-approved { background: #bbf7d0; color: #15803d; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background: var(--bg-light);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }
        
        @media (max-width: 768px) {
            .wallet-amount {
                font-size: 2rem;
            }
            
            .platform-option {
                margin-bottom: 1rem;
            }
        }
        
        /* Wizard Styles */
        .wizard-progress {
            position: relative;
        }
        
        .wizard-steps {
            position: relative;
        }
        
        .wizard-step {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .wizard-step .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 0.5rem;
            transition: all 0.3s ease;
        }
        
        .wizard-step.active .step-number {
            background: var(--primary-color);
            color: white;
        }
        
        .wizard-step.completed .step-number {
            background: var(--success-color);
            color: white;
        }
        
        .wizard-step .step-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
        }
        
        .wizard-step.active .step-label {
            color: var(--primary-color);
        }
        
        .wizard-step.completed .step-label {
            color: var(--success-color);
        }
        
        /* Platform Cards */
        .platform-card {
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            height: 100%;
        }
        
        .platform-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .platform-card.selected {
            border-color: var(--primary-color);
            background: #eff6ff;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }
        
        .platform-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .platform-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .platform-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .platform-content h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        .platform-stats {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .platform-stats .stat {
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        .platform-stats .stat i {
            color: var(--accent-color);
            margin-right: 0.25rem;
        }
        
        .platform-pricing {
            text-align: center;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .platform-pricing .price {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }
        
        .platform-pricing .duration {
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        /* Country Grid */
        .country-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .country-item {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .country-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .country-item.selected {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .country-item img {
            width: 40px;
            height: 30px;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .country-item span {
            display: block;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        /* Screenshot Upload */
        .screenshot-upload-area {
            border: 2px dashed #e5e7eb;
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: white;
            cursor: pointer;
        }
        
        .screenshot-upload-area:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .upload-preview {
            margin-top: 1rem;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        /* Review Cards */
        .review-card {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            position: relative;
        }
        
        .review-card:hover {
            border-color: var(--primary-color);
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .review-header h6 {
            margin: 0;
            color: var(--text-dark);
            font-weight: 600;
        }
        
        /* Order Summary */
        .order-summary .card {
            border: none;
            background: #f8fafc;
        }
        
        .order-summary h6 {
            color: var(--text-dark);
            font-weight: 600;
        }
        
        /* Confirmation */
        .total-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 1rem 0;
        }
        
        /* Wizard Actions */
        .wizard-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        @media (max-width: 768px) {
            .wizard-actions {
                flex-direction: column;
                gap: 1rem;
            }
            
            .wizard-actions .btn {
                width: 100%;
            }
            
            .country-grid {
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            }
            
            .platform-card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($user['name']); ?>
                </span>
                <a class="nav-link me-3" href="my_account.php">
                    <i class="fas fa-cog me-2"></i>My Account
                </a>
                <a class="nav-link" href="/logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Wallet Balance Card -->
            <div class="wallet-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">Wallet Balance</h4>
                        <div class="wallet-amount">$
<?php 
$balance = isset($user['wallet_balance']) && $user['wallet_balance'] !== null ? $user['wallet_balance'] : 0.00;
echo number_format((float)$balance, 2);
?>
</div>
                        <p class="mb-0">Add funds to post review requests</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addBalanceModal">
                            <i class="fas fa-plus me-2"></i>Add Balance
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Post Reviews Form -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Post Review Requests</h5>
                        </div>
                        <div class="card-body">
                            <!-- Order Placement Wizard -->
                            <div class="order-wizard">
                                <!-- Progress Bar -->
                                <div class="wizard-progress mb-4">
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar" id="wizardProgress" role="progressbar" style="width: 25%"></div>
                                    </div>
                                    <div class="wizard-steps d-flex justify-content-between mt-2">
                                        <div class="wizard-step active" data-step="1">
                                            <div class="step-number">1</div>
                                            <div class="step-label">Platform</div>
                                        </div>
                                        <div class="wizard-step" data-step="2">
                                            <div class="step-number">2</div>
                                            <div class="step-label">Business Info</div>
                                        </div>
                                        <div class="wizard-step" data-step="3">
                                            <div class="step-number">3</div>
                                            <div class="step-label">Reviews</div>
                                        </div>
                                        <div class="wizard-step" data-step="4">
                                            <div class="step-number">4</div>
                                            <div class="step-label">Confirm</div>
                                        </div>
                                    </div>
                                </div>

                                <form method="post" enctype="multipart/form-data" id="reviewForm">
                                    <!-- Step 1: Platform Selection -->
                                    <div class="wizard-step-content" id="step1">
                                        <div class="text-center mb-4">
                                            <h4><i class="fas fa-globe me-2"></i>Choose Your Platform</h4>
                                            <p class="text-muted">Select the platform where you want reviews posted</p>
                                        </div>
                                        
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="google">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fab fa-google"></i>
                                                        </div>
                                                        <div class="platform-badge">Most Popular</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>Google My Business</h6>
                                                        <p class="text-muted small">Google Reviews & Maps</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.8/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 10K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$15 - $50</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="trustpilot">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fas fa-shield-alt"></i>
                                                        </div>
                                                        <div class="platform-badge">Trusted</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>Trustpilot</h6>
                                                        <p class="text-muted small">Trust & Safety Reviews</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.9/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 5K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$20 - $60</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="yelp">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fab fa-yelp"></i>
                                                        </div>
                                                        <div class="platform-badge">Local</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>Yelp</h6>
                                                        <p class="text-muted small">Local Business Reviews</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.7/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 8K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$12 - $45</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="facebook">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fab fa-facebook"></i>
                                                        </div>
                                                        <div class="platform-badge">Social</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>Facebook</h6>
                                                        <p class="text-muted small">Social Media Reviews</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.6/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 15K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$8 - $35</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="tripadvisor">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fas fa-plane"></i>
                                                        </div>
                                                        <div class="platform-badge">Travel</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>TripAdvisor</h6>
                                                        <p class="text-muted small">Travel & Hospitality</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.5/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 6K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$10 - $40</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="platform-card" data-platform="other">
                                                    <div class="platform-header">
                                                        <div class="platform-icon">
                                                            <i class="fas fa-globe"></i>
                                                        </div>
                                                        <div class="platform-badge">Custom</div>
                                                    </div>
                                                    <div class="platform-content">
                                                        <h6>Other Platform</h6>
                                                        <p class="text-muted small">Custom Platform</p>
                                                        <div class="platform-stats">
                                                            <span class="stat"><i class="fas fa-star"></i> 4.5/5</span>
                                                            <span class="stat"><i class="fas fa-users"></i> 3K+</span>
                                                        </div>
                                                        <div class="platform-pricing">
                                                            <span class="price">$5 - $50</span>
                                                            <span class="duration">per review</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <input type="hidden" name="platform" id="selectedPlatform" required>
                                        
                                        <div class="wizard-actions mt-4">
                                            <button type="button" class="btn btn-primary btn-lg" onclick="nextStep()" disabled id="nextBtn1">
                                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Step 2: Business Information -->
                                    <div class="wizard-step-content" id="step2" style="display: none;">
                                        <div class="text-center mb-4">
                                            <h4><i class="fas fa-building me-2"></i>Business Information</h4>
                                            <p class="text-muted">Tell us about your business</p>
                                        </div>
                                        
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">Business Name *</label>
                                                <input type="text" name="business_name" class="form-control form-control-lg" required>
                                                <div class="form-text">Enter your business name exactly as it appears online</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label fw-bold">Business Profile Link</label>
                                                <input type="url" name="business_link" class="form-control form-control-lg" placeholder="https://...">
                                                <div class="form-text">Optional: Link to your business profile</div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label class="form-label fw-bold">Select Country *</label>
                                                <div class="country-grid">
                                                    <div class="country-item" data-country="USA">
                                                        <img src="https://flagcdn.com/w40/us.png" alt="USA">
                                                        <span>USA</span>
                                                    </div>
                                                    <div class="country-item" data-country="UK">
                                                        <img src="https://flagcdn.com/w40/gb.png" alt="UK">
                                                        <span>UK</span>
                                                    </div>
                                                    <div class="country-item" data-country="Canada">
                                                        <img src="https://flagcdn.com/w40/ca.png" alt="Canada">
                                                        <span>Canada</span>
                                                    </div>
                                                    <div class="country-item" data-country="Australia">
                                                        <img src="https://flagcdn.com/w40/au.png" alt="Australia">
                                                        <span>Australia</span>
                                                    </div>
                                                    <div class="country-item" data-country="Germany">
                                                        <img src="https://flagcdn.com/w40/de.png" alt="Germany">
                                                        <span>Germany</span>
                                                    </div>
                                                    <div class="country-item" data-country="France">
                                                        <img src="https://flagcdn.com/w40/fr.png" alt="France">
                                                        <span>France</span>
                                                    </div>
                                                    <div class="country-item" data-country="India">
                                                        <img src="https://flagcdn.com/w40/in.png" alt="India">
                                                        <span>India</span>
                                                    </div>
                                                    <div class="country-item" data-country="Singapore">
                                                        <img src="https://flagcdn.com/w40/sg.png" alt="Singapore">
                                                        <span>Singapore</span>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="country" id="selectedCountry" required>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label class="form-label fw-bold">Business Profile Screenshot *</label>
                                                <div class="screenshot-upload-area" onclick="document.getElementById('screenshot').click()">
                                                    <div class="upload-content">
                                                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                                        <h5>Upload Screenshot</h5>
                                                        <p class="text-muted">Click to upload or drag and drop</p>
                                                        <p class="text-muted small">PNG, JPG up to 5MB</p>
                                                    </div>
                                                    <div class="upload-preview" id="screenshotPreview" style="display: none;">
                                                        <img src="" alt="Preview" class="preview-image">
                                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeScreenshot()">
                                                            <i class="fas fa-times"></i> Remove
                                                        </button>
                                                    </div>
                                                </div>
                                                <input type="file" name="screenshot" id="screenshot" class="d-none" accept="image/*" required>
                                                <div class="form-text">Upload a screenshot of your business profile page</div>
                                            </div>
                                        </div>
                                        
                                        <div class="wizard-actions mt-4">
                                            <button type="button" class="btn btn-outline-secondary btn-lg" onclick="prevStep()">
                                                <i class="fas fa-arrow-left me-2"></i> Previous
                                            </button>
                                            <button type="button" class="btn btn-primary btn-lg" onclick="nextStep()" disabled id="nextBtn2">
                                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Step 3: Review Content -->
                                    <div class="wizard-step-content" id="step3" style="display: none;">
                                        <div class="text-center mb-4">
                                            <h4><i class="fas fa-star me-2"></i>Review Content</h4>
                                            <p class="text-muted">Add your review content and set pricing</p>
                                        </div>
                                        
                                        <div id="reviewsContainer">
                                            <div class="review-card" data-review="0">
                                                <div class="review-header">
                                                    <h6>Review #1</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReview(0)" style="display: none;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="row g-3">
                                                    <div class="col-md-8">
                                                        <label class="form-label">Review Text *</label>
                                                        <textarea name="review_text_0" class="form-control" rows="3" placeholder="Write your review content here..." required></textarea>
                                                        <div class="form-text">Minimum 50 characters, maximum 500 characters</div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">Amount ($) *</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">$</span>
                                                            <input type="number" name="review_amount_0" class="form-control" min="1" step="0.01" placeholder="25.00" required>
                                                        </div>
                                                        <div class="price-range-info mt-2">
                                                            <small class="text-muted">Range: <span id="priceRange0">$15 - $50</span></small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="add-review-section text-center mt-4">
                                            <button type="button" class="btn btn-outline-primary btn-lg" onclick="addReview()">
                                                <i class="fas fa-plus-circle me-2"></i>Add Another Review
                                            </button>
                                            <p class="text-muted mt-2">You can add up to 10 reviews per order</p>
                                        </div>
                                        
                                        <div class="order-summary mt-4">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6><i class="fas fa-calculator me-2"></i>Order Summary</h6>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p class="mb-1"><strong>Platform:</strong> <span id="summaryPlatform">-</span></p>
                                                            <p class="mb-1"><strong>Business:</strong> <span id="summaryBusiness">-</span></p>
                                                            <p class="mb-1"><strong>Country:</strong> <span id="summaryCountry">-</span></p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p class="mb-1"><strong>Reviews:</strong> <span id="summaryReviews">1</span></p>
                                                            <p class="mb-1"><strong>Total Amount:</strong> <span id="summaryTotal">$0.00</span></p>
                                                            <p class="mb-1"><strong>Wallet Balance:</strong> <span id="summaryBalance">$<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?></span></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="wizard-actions mt-4">
                                            <button type="button" class="btn btn-outline-secondary btn-lg" onclick="prevStep()">
                                                <i class="fas fa-arrow-left me-2"></i> Previous
                                            </button>
                                            <button type="button" class="btn btn-primary btn-lg" onclick="nextStep()" disabled id="nextBtn3">
                                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Step 4: Confirmation -->
                                    <div class="wizard-step-content" id="step4" style="display: none;">
                                        <div class="text-center mb-4">
                                            <h4><i class="fas fa-check-circle me-2"></i>Confirm Your Order</h4>
                                            <p class="text-muted">Review and confirm your order details</p>
                                        </div>
                                        
                                        <div class="order-confirmation">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h6><i class="fas fa-list me-2"></i>Order Details</h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div id="confirmationDetails">
                                                                <!-- Will be populated by JavaScript -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="card bg-primary text-white">
                                                        <div class="card-body text-center">
                                                            <h5>Total Amount</h5>
                                                            <div class="total-amount" id="confirmationTotal">$0.00</div>
                                                            <p class="mb-3">Will be deducted from your wallet</p>
                                                            <button type="submit" name="post_reviews" class="btn btn-light btn-lg w-100">
                                                                <i class="fas fa-paper-plane me-2"></i>Confirm Order
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="wizard-actions mt-4">
                                            <button type="button" class="btn btn-outline-secondary btn-lg" onclick="prevStep()">
                                                <i class="fas fa-arrow-left me-2"></i> Previous
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Reviews -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Reviews</h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($reviews)): ?>
                                <div class="p-4 text-center text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <h6>No reviews posted yet</h6>
                                    <p class="small">Start by posting your first review request</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Platform</th>
                                                <th>Business</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($reviews, 0, 5) as $r): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fab fa-<?php echo strtolower($r['platform']); ?> me-2"></i>
                                                        <?php echo htmlspecialchars($r['platform']); ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($r['business_name']); ?></td>
                                                    <td>$<?php echo number_format($r['amount'], 2); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $r['status']; ?>">
                                                            <?php echo ucfirst($r['status']); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php if (count($reviews) > 5): ?>
                                    <div class="p-3 text-center">
                                        <a href="#" class="btn btn-outline-primary btn-sm">View All Reviews</a>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Balance Modal -->
    <div class="modal fade" id="addBalanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Balance</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Amount ($)</label>
                            <input type="number" name="balance_amount" class="form-control" min="1" step="0.01" placeholder="100.00" required>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Add funds to your wallet to post review requests. You can add any amount starting from $1.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_balance" class="btn btn-success">Add Balance</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let reviewCount = 1;
        let currentStep = 1;
        
        // Wizard Navigation
        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < 4) {
                    document.getElementById(`step${currentStep}`).style.display = 'none';
                    currentStep++;
                    document.getElementById(`step${currentStep}`).style.display = 'block';
                    updateWizardProgress();
                    updateSummary();
                }
            }
        }
        
        function prevStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).style.display = 'none';
                currentStep--;
                document.getElementById(`step${currentStep}`).style.display = 'block';
                updateWizardProgress();
            }
        }
        
        function updateWizardProgress() {
            const progress = (currentStep / 4) * 100;
            document.getElementById('wizardProgress').style.width = progress + '%';
            
            // Update step indicators
            document.querySelectorAll('.wizard-step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
        }
        
        function validateCurrentStep() {
            switch(currentStep) {
                case 1:
                    const platform = document.getElementById('selectedPlatform').value;
                    if (!platform) {
                        alert('Please select a platform');
                        return false;
                    }
                    break;
                case 2:
                    const businessName = document.querySelector('input[name="business_name"]').value;
                    const country = document.getElementById('selectedCountry').value;
                    const screenshot = document.getElementById('screenshot').files[0];
                    
                    if (!businessName.trim()) {
                        alert('Please enter your business name');
                        return false;
                    }
                    if (!country) {
                        alert('Please select a country');
                        return false;
                    }
                    if (!screenshot) {
                        alert('Please upload a business profile screenshot');
                        return false;
                    }
                    break;
                case 3:
                    const reviews = document.querySelectorAll('textarea[name^="review_text_"]');
                    const amounts = document.querySelectorAll('input[name^="review_amount_"]');
                    
                    for (let i = 0; i < reviews.length; i++) {
                        if (!reviews[i].value.trim()) {
                            alert(`Please enter review text for review #${i + 1}`);
                            return false;
                        }
                        if (!amounts[i].value || parseFloat(amounts[i].value) <= 0) {
                            alert(`Please enter a valid amount for review #${i + 1}`);
                            return false;
                        }
                    }
                    break;
            }
            return true;
        }
        
        // Platform selection
        document.querySelectorAll('.platform-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.platform-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('selectedPlatform').value = this.dataset.platform;
                document.getElementById('nextBtn1').disabled = false;
                updateSummary();
            });
        });
        
        // Country selection
        document.querySelectorAll('.country-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.country-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('selectedCountry').value = this.dataset.country;
                updateSummary();
            });
        });
        
        // Screenshot upload
        document.getElementById('screenshot').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.querySelector('.upload-content').style.display = 'none';
                    document.getElementById('screenshotPreview').style.display = 'block';
                    document.querySelector('.preview-image').src = e.target.result;
                };
                reader.readAsDataURL(file);
                updateSummary();
            }
        });
        
        function removeScreenshot() {
            document.getElementById('screenshot').value = '';
            document.querySelector('.upload-content').style.display = 'block';
            document.getElementById('screenshotPreview').style.display = 'none';
            updateSummary();
        }
        
        // Add review
        function addReview() {
            if (reviewCount >= 10) {
                alert('Maximum 10 reviews allowed per order');
                return;
            }
            
            const container = document.getElementById('reviewsContainer');
            const reviewCard = document.createElement('div');
            reviewCard.className = 'review-card';
            reviewCard.dataset.review = reviewCount;
            reviewCard.innerHTML = `
                <div class="review-header">
                    <h6>Review #${reviewCount + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeReview(${reviewCount})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="row g-3">
                    <div class="col-md-8">
                        <label class="form-label">Review Text *</label>
                        <textarea name="review_text_${reviewCount}" class="form-control" rows="3" placeholder="Write your review content here..." required></textarea>
                        <div class="form-text">Minimum 50 characters, maximum 500 characters</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Amount ($) *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" name="review_amount_${reviewCount}" class="form-control" min="1" step="0.01" placeholder="25.00" required>
                        </div>
                        <div class="price-range-info mt-2">
                            <small class="text-muted">Range: <span id="priceRange${reviewCount}">$15 - $50</span></small>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(reviewCard);
            
            // Apply price validation
            const selectedPlatform = document.getElementById('selectedPlatform').value;
            if (selectedPlatform) {
                updatePriceValidation(selectedPlatform);
            }
            
            reviewCount++;
            updateSummary();
        }
        
        // Remove review
        function removeReview(index) {
            const reviewCard = document.querySelector(`[data-review="${index}"]`);
            if (reviewCard) {
                reviewCard.remove();
                reviewCount--;
                updateSummary();
            }
        }
        
        // Update summary
        function updateSummary() {
            const platform = document.getElementById('selectedPlatform').value;
            const businessName = document.querySelector('input[name="business_name"]').value;
            const country = document.getElementById('selectedCountry').value;
            
            document.getElementById('summaryPlatform').textContent = platform ? platform.charAt(0).toUpperCase() + platform.slice(1) : '-';
            document.getElementById('summaryBusiness').textContent = businessName || '-';
            document.getElementById('summaryCountry').textContent = country || '-';
            document.getElementById('summaryReviews').textContent = reviewCount;
            
            // Calculate total
            let total = 0;
            document.querySelectorAll('input[name^="review_amount_"]').forEach(input => {
                total += parseFloat(input.value) || 0;
            });
            
            document.getElementById('summaryTotal').textContent = '$' + total.toFixed(2);
            document.getElementById('confirmationTotal').textContent = '$' + total.toFixed(2);
            
            // Update confirmation details
            updateConfirmationDetails();
        }
        
        function updateConfirmationDetails() {
            const platform = document.getElementById('selectedPlatform').value;
            const businessName = document.querySelector('input[name="business_name"]').value;
            const businessLink = document.querySelector('input[name="business_link"]').value;
            const country = document.getElementById('selectedCountry').value;
            
            let details = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Platform:</strong> ${platform ? platform.charAt(0).toUpperCase() + platform.slice(1) : '-'}
                    </div>
                    <div class="col-md-6">
                        <strong>Business Name:</strong> ${businessName || '-'}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Country:</strong> ${country || '-'}
                    </div>
                    <div class="col-md-6">
                        <strong>Business Link:</strong> ${businessLink || 'Not provided'}
                    </div>
                </div>
                <hr>
                <h6>Reviews:</h6>
            `;
            
            document.querySelectorAll('textarea[name^="review_text_"]').forEach((textarea, index) => {
                const amount = document.querySelector(`input[name="review_amount_${index}"]`).value;
                details += `
                    <div class="review-confirmation mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <strong>Review #${index + 1}:</strong>
                                <p class="mb-1">${textarea.value || 'No text provided'}</p>
                            </div>
                            <div class="text-end">
                                <strong>$${parseFloat(amount || 0).toFixed(2)}</strong>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('confirmationDetails').innerHTML = details;
        }
        
        // Price validation data
        const platformPricing = {
            'google': { min: 15.00, max: 50.00, default: 25.00 },
            'trustpilot': { min: 20.00, max: 60.00, default: 30.00 },
            'tripadvisor': { min: 10.00, max: 40.00, default: 20.00 },
            'yelp': { min: 12.00, max: 45.00, default: 22.00 },
            'facebook': { min: 8.00, max: 35.00, default: 18.00 },
            'other': { min: 5.00, max: 50.00, default: 15.00 }
        };
        
        function updatePriceValidation(platform) {
            const pricing = platformPricing[platform];
            if (!pricing) return;
            
            document.querySelectorAll('input[name^="review_amount_"]').forEach((input, index) => {
                input.min = pricing.min;
                input.max = pricing.max;
                input.placeholder = pricing.default.toFixed(2);
                
                const rangeSpan = document.getElementById(`priceRange${index}`);
                if (rangeSpan) {
                    rangeSpan.textContent = `$${pricing.min.toFixed(2)} - $${pricing.max.toFixed(2)}`;
                }
                
                input.addEventListener('input', function() {
                    validatePrice(this, pricing);
                    updateSummary();
                });
            });
        }
        
        function validatePrice(input, pricing) {
            const value = parseFloat(input.value);
            const feedback = input.parentNode.querySelector('.price-feedback');
            
            if (feedback) {
                feedback.remove();
            }
            
            if (isNaN(value)) return;
            
            const newFeedback = document.createElement('div');
            newFeedback.className = 'price-feedback mt-1';
            
            if (value < pricing.min) {
                newFeedback.innerHTML = `<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Minimum price for this platform is $${pricing.min.toFixed(2)}</small>`;
                input.classList.add('is-invalid');
            } else if (value > pricing.max) {
                newFeedback.innerHTML = `<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Maximum price for this platform is $${pricing.max.toFixed(2)}</small>`;
                input.classList.add('is-invalid');
            } else {
                newFeedback.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> Price is within valid range</small>`;
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
            
            input.parentNode.appendChild(newFeedback);
        }
        
        // Form validation
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            if (!validateCurrentStep()) {
                e.preventDefault();
                return;
            }
            
            const total = parseFloat(document.getElementById('summaryTotal').textContent.replace('$', ''));
            const balance = parseFloat(document.getElementById('summaryBalance').textContent.replace('$', ''));
            
            if (total > balance) {
                alert('Insufficient wallet balance. Please add more funds.');
                e.preventDefault();
                return;
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateWizardProgress();
            updateSummary();
        });
    </script>
</body>
</html>

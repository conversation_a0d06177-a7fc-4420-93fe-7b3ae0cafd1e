<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Admin Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-sm);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
            text-decoration: none;
        }
        
        .navbar-nav .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
        }
        
        .navbar-nav .nav-link:hover {
            background: var(--bg-light);
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
            min-height: calc(100vh - 80px);
        }
        
        .container {
            max-width: 1400px;
        }
        
        .card {
            background: var(--bg-white);
            border: none;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .card-header {
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #059669, var(--success-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
        }
        
        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706, var(--warning-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, var(--danger-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
            color: white;
        }
        
        .btn-outline-secondary {
            border: 2px solid var(--border-color);
            color: var(--text-dark);
            background: transparent;
        }
        
        .btn-outline-secondary:hover {
            background: var(--bg-light);
            border-color: var(--text-light);
            color: var(--text-dark);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .stats-card > * {
            position: relative;
            z-index: 1;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.875rem 1rem;
            transition: all 0.3s ease;
            background: var(--bg-white);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        /* Table Styles */
        .table {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .table th {
            background: var(--bg-light);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
            padding: 1rem;
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
            padding: 1rem;
        }
        
        .table tbody tr:hover {
            background: var(--bg-light);
        }
        
        /* Status Badges */
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-open { background: #fef3c7; color: #92400e; }
        .status-claimed { background: #dbeafe; color: #1e40af; }
        .status-submitted { background: #d1fae5; color: #065f46; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-approved { background: #bbf7d0; color: #15803d; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        /* Alerts */
        .alert {
            border: none;
            border-radius: var(--radius-lg);
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .alert-danger {
            background: #fecaca;
            color: #991b1b;
        }
        
        .alert-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-title {
            font-weight: 600;
            color: var(--text-dark);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .stat-number {
                font-size: 2rem;
            }
            
            .table-responsive {
                border-radius: var(--radius-lg);
            }
        }
        
        @media (max-width: 576px) {
            .stats-card {
                padding: 1.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .table th,
            .table td {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews Admin
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-shield me-2"></i>{{ $user->name }}
                </span>
                <a class="nav-link me-3" href="/admin/settings">
                    <i class="fas fa-cog me-2"></i>Settings
                </a>
                <a class="nav-link" href="/logout">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            @if(session('success'))
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>{!! session('error') !!}
                </div>
            @endif
            
            <!-- Stats Cards -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="stats-card text-center">
                        <div class="stat-number">{{ $stats['total_reviews'] ?? 0 }}</div>
                        <p class="mb-0 opacity-75">Total Reviews</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card text-center">
                        <div class="stat-number">{{ $stats['pending_reviews'] ?? 0 }}</div>
                        <p class="mb-0 opacity-75">Pending Reviews</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card text-center">
                        <div class="stat-number">{{ $stats['total_users'] ?? 0 }}</div>
                        <p class="mb-0 opacity-75">Total Users</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card text-center">
                        <div class="stat-number">${{ number_format($stats['total_revenue'] ?? 0, 2) }}</div>
                        <p class="mb-0 opacity-75">Total Revenue</p>
                    </div>
                </div>
            </div>

            <!-- Reviews Management -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>Reviews Management
                            </h5>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterReviews('all')">All</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterReviews('open')">Open</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterReviews('claimed')">Claimed</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterReviews('submitted')">Submitted</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="filterReviews('completed')">Completed</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Platform</th>
                                            <th>Business</th>
                                            <th>Customer</th>
                                            <th>Reviewer</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reviews as $review)
                                            <tr data-status="{{ $review->status }}">
                                                <td>#{{ $review->id }}</td>
                                                <td>
                                                    <i class="fab fa-{{ $review->platform }} me-2"></i>{{ ucfirst($review->platform) }}
                                                </td>
                                                <td>
                                                    <strong>{{ $review->business_name }}</strong><br>
                                                    <small class="text-muted">{{ $review->business_url }}</small>
                                                </td>
                                                <td>{{ $review->customer->name ?? 'N/A' }}</td>
                                                <td>{{ $review->reviewer->name ?? 'Unassigned' }}</td>
                                                <td>${{ number_format($review->total_amount, 2) }}</td>
                                                <td>
                                                    <span class="status-badge status-{{ $review->status }}">
                                                        {{ ucfirst($review->status) }}
                                                    </span>
                                                </td>
                                                <td>{{ $review->created_at->format('M d, Y') }}</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="viewReviewDetails({{ $review->id }})">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        @if($review->status === 'submitted')
                                                            <button class="btn btn-sm btn-success" onclick="approveReview({{ $review->id }})">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-danger" onclick="rejectReview({{ $review->id }})">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>Users Management
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Wallet Balance</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($users as $userItem)
                                            <tr>
                                                <td>#{{ $userItem->id }}</td>
                                                <td>{{ $userItem->name }}</td>
                                                <td>{{ $userItem->email }}</td>
                                                <td>
                                                    <span class="badge bg-{{ $userItem->role === 'admin' ? 'danger' : ($userItem->role === 'reviewer' ? 'warning' : 'primary') }}">
                                                        {{ ucfirst($userItem->role) }}
                                                    </span>
                                                </td>
                                                <td>${{ number_format($userItem->wallet_balance ?? 0, 2) }}</td>
                                                <td>{{ $userItem->created_at->format('M d, Y') }}</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="viewUserDetails({{ $userItem->id }})">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" onclick="editUser({{ $userItem->id }})">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Details Modal -->
    <div class="modal fade" id="reviewDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>Review Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="reviewDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterReviews(status) {
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            
            // Update active button
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-secondary');
            });
            event.target.classList.remove('btn-outline-secondary');
            event.target.classList.add('btn-primary');
        }

        function viewReviewDetails(reviewId) {
            fetch(`/admin/review-details/${reviewId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('reviewDetailsContent').innerHTML = data.html;
                        new bootstrap.Modal(document.getElementById('reviewDetailsModal')).show();
                    } else {
                        showAlert(data.message || 'Error loading review details', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error loading review details', 'error');
                });
        }

        function approveReview(reviewId) {
            if (confirm('Are you sure you want to approve this review?')) {
                fetch(`/admin/approve-review/${reviewId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Review approved successfully!', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert(data.message || 'Error approving review', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error approving review', 'error');
                });
            }
        }

        function rejectReview(reviewId) {
            const reason = prompt('Please provide a reason for rejection:');
            if (reason) {
                fetch(`/admin/reject-review/${reviewId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ reason: reason })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Review rejected successfully!', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert(data.message || 'Error rejecting review', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error rejecting review', 'error');
                });
            }
        }

        function viewUserDetails(userId) {
            // Implement user details view
            showAlert('User details feature coming soon!', 'info');
        }

        function editUser(userId) {
            // Implement user edit
            showAlert('User edit feature coming soon!', 'info');
        }

        // Custom alert function
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on load
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html> 
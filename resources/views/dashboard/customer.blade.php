<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Customer Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-sm);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
            text-decoration: none;
        }
        
        .navbar-nav .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--radius-md);
            padding: 0.5rem 1rem;
        }
        
        .navbar-nav .nav-link:hover {
            background: var(--bg-light);
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
            min-height: calc(100vh - 80px);
        }
        
        .container {
            max-width: 1200px;
        }
        
        .card {
            background: var(--bg-white);
            border: none;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .card-header {
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }
        
        .btn-success:hover {
            background: linear-gradient(135deg, #059669, var(--success-color));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            color: white;
        }
        
        .btn-outline-secondary {
            border: 2px solid var(--border-color);
            color: var(--text-dark);
            background: transparent;
        }
        
        .btn-outline-secondary:hover {
            background: var(--bg-light);
            border-color: var(--text-light);
            color: var(--text-dark);
        }
        
        .btn-light {
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-dark);
            backdrop-filter: blur(10px);
        }
        
        .btn-light:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .wallet-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .wallet-card > * {
            position: relative;
            z-index: 1;
        }
        
        .wallet-amount {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.875rem 1rem;
            transition: all 0.3s ease;
            background: var(--bg-white);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        /* Wizard Styles */
        .wizard-progress {
            margin-bottom: 2rem;
        }
        
        .wizard-steps {
            position: relative;
        }
        
        .wizard-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--border-color);
            z-index: 1;
        }
        
        .wizard-step {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .wizard-step .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--bg-white);
            border: 2px solid var(--border-color);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 0.5rem;
            transition: all 0.3s ease;
        }
        
        .wizard-step.active .step-number {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .wizard-step.completed .step-number {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }
        
        .wizard-step .step-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-light);
        }
        
        .wizard-step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .wizard-step.completed .step-label {
            color: var(--success-color);
        }
        
        /* Platform Cards */
        .platform-card {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-white);
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .platform-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .platform-card:hover::before {
            transform: scaleX(1);
        }
        
        .platform-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .platform-card.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #eff6ff, #f8fafc);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        
        .platform-card.selected::before {
            transform: scaleX(1);
        }
        
        .platform-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .platform-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .platform-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .platform-content h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        .platform-stats {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .platform-stats .stat {
            font-size: 0.875rem;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .platform-stats .stat i {
            color: var(--accent-color);
        }
        
        .platform-pricing {
            text-align: center;
            padding: 1rem;
            background: var(--bg-light);
            border-radius: var(--radius-md);
            margin-top: 1rem;
        }
        
        .platform-pricing .price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }
        
        .platform-pricing .duration {
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        /* Country Grid */
        .country-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .country-item {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-white);
        }
        
        .country-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .country-item.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #eff6ff, #f8fafc);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        
        .country-item img {
            width: 48px;
            height: 36px;
            border-radius: var(--radius-sm);
            margin-bottom: 0.75rem;
            box-shadow: var(--shadow-sm);
        }
        
        .country-item span {
            display: block;
            font-weight: 500;
            color: var(--text-dark);
            font-size: 0.875rem;
        }
        
        /* Review Cards */
        .review-card {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: var(--bg-white);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .review-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .review-header h6 {
            margin: 0;
            color: var(--text-dark);
            font-weight: 600;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-open { background: #fef3c7; color: #92400e; }
        .status-claimed { background: #dbeafe; color: #1e40af; }
        .status-submitted { background: #d1fae5; color: #065f46; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-approved { background: #bbf7d0; color: #15803d; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        /* Order Summary */
        .order-summary .card {
            border: none;
            background: var(--bg-light);
        }
        
        .order-summary h6 {
            color: var(--text-dark);
            font-weight: 600;
        }
        
        .total-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 1rem 0;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Wizard Actions */
        .wizard-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }
        
        /* Alerts */
        .alert {
            border: none;
            border-radius: var(--radius-lg);
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .alert-danger {
            background: #fecaca;
            color: #991b1b;
        }
        
        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-title {
            font-weight: 600;
            color: var(--text-dark);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .wallet-amount {
                font-size: 2rem;
            }
            
            .platform-card {
                margin-bottom: 1rem;
            }
            
            .wizard-actions {
                flex-direction: column;
                gap: 1rem;
            }
            
            .wizard-actions .btn {
                width: 100%;
            }
            
            .country-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
            
            .platform-stats {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
        
        @media (max-width: 576px) {
            .wallet-card {
                padding: 1.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .platform-card {
                padding: 1rem;
            }
            
            .country-item {
                padding: 1rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-2"></i>{{ $user->name }}
                </span>
                <a class="nav-link me-3" href="/my-account">
                    <i class="fas fa-cog me-2"></i>My Account
                </a>
                <a class="nav-link" href="/logout">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            @if(session('success'))
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>{!! session('error') !!}
                </div>
            @endif
            
            <!-- Wallet Balance Card -->
            <div class="wallet-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">
                            <i class="fas fa-wallet me-2"></i>Wallet Balance
                        </h4>
                        <div class="wallet-amount">${{ number_format($user->wallet_balance ?? 0, 2) }}</div>
                        <p class="mb-0 opacity-75">Add funds to post review requests</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addBalanceModal">
                            <i class="fas fa-plus me-2"></i>Add Balance
                        </button>
                    </div>
                </div>
            </div>

            <!-- Review Posting Section -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-plus-circle me-2"></i>Post New Review Request
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Wizard Progress -->
                            <div class="wizard-progress">
                                <div class="row wizard-steps">
                                    <div class="col-3 wizard-step active" id="step-1">
                                        <div class="step-number">1</div>
                                        <div class="step-label">Platform</div>
                                    </div>
                                    <div class="col-3 wizard-step" id="step-2">
                                        <div class="step-number">2</div>
                                        <div class="step-label">Country</div>
                                    </div>
                                    <div class="col-3 wizard-step" id="step-3">
                                        <div class="step-number">3</div>
                                        <div class="step-label">Details</div>
                                    </div>
                                    <div class="col-3 wizard-step" id="step-4">
                                        <div class="step-number">4</div>
                                        <div class="step-label">Confirm</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 1: Platform Selection -->
                            <div class="wizard-content" id="wizard-step-1">
                                <h6 class="mb-3">
                                    <i class="fas fa-globe me-2"></i>Select Platform
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="google">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fab fa-google"></i>
                                                </div>
                                                <span class="platform-badge">Popular</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Google Reviews</h6>
                                                <p class="text-muted mb-2">Get authentic Google reviews for your business</p>
                                                <div class="platform-stats">
                                                    <span class="stat"><i class="fas fa-clock"></i>24-48 hours</span>
                                                    <span class="stat"><i class="fas fa-star"></i>5-star reviews</span>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">$15.00</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="trustpilot">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fas fa-shield-alt"></i>
                                                </div>
                                                <span class="platform-badge">Trusted</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Trustpilot Reviews</h6>
                                                <p class="text-muted mb-2">Build trust with Trustpilot reviews</p>
                                                <div class="platform-stats">
                                                    <span class="stat"><i class="fas fa-clock"></i>24-48 hours</span>
                                                    <span class="stat"><i class="fas fa-star"></i>5-star reviews</span>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">$20.00</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="facebook">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fab fa-facebook"></i>
                                                </div>
                                                <span class="platform-badge">Social</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Facebook Reviews</h6>
                                                <p class="text-muted mb-2">Boost your Facebook page reputation</p>
                                                <div class="platform-stats">
                                                    <span class="stat"><i class="fas fa-clock"></i>24-48 hours</span>
                                                    <span class="stat"><i class="fas fa-star"></i>5-star reviews</span>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">$12.00</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="platform-card" data-platform="yelp">
                                            <div class="platform-header">
                                                <div class="platform-icon">
                                                    <i class="fas fa-utensils"></i>
                                                </div>
                                                <span class="platform-badge">Local</span>
                                            </div>
                                            <div class="platform-content">
                                                <h6>Yelp Reviews</h6>
                                                <p class="text-muted mb-2">Perfect for local businesses</p>
                                                <div class="platform-stats">
                                                    <span class="stat"><i class="fas fa-clock"></i>24-48 hours</span>
                                                    <span class="stat"><i class="fas fa-star"></i>5-star reviews</span>
                                                </div>
                                                <div class="platform-pricing">
                                                    <span class="price">$18.00</span>
                                                    <span class="duration">per review</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="wizard-actions">
                                    <div></div>
                                    <button class="btn btn-primary" onclick="nextStep()">
                                        Next <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Country Selection -->
                            <div class="wizard-content d-none" id="wizard-step-2">
                                <h6 class="mb-3">
                                    <i class="fas fa-flag me-2"></i>Select Country
                                </h6>
                                <div class="country-grid">
                                    <div class="country-item" data-country="US">
                                        <img src="https://flagcdn.com/us.svg" alt="United States">
                                        <span>United States</span>
                                    </div>
                                    <div class="country-item" data-country="CA">
                                        <img src="https://flagcdn.com/ca.svg" alt="Canada">
                                        <span>Canada</span>
                                    </div>
                                    <div class="country-item" data-country="GB">
                                        <img src="https://flagcdn.com/gb.svg" alt="United Kingdom">
                                        <span>United Kingdom</span>
                                    </div>
                                    <div class="country-item" data-country="AU">
                                        <img src="https://flagcdn.com/au.svg" alt="Australia">
                                        <span>Australia</span>
                                    </div>
                                    <div class="country-item" data-country="DE">
                                        <img src="https://flagcdn.com/de.svg" alt="Germany">
                                        <span>Germany</span>
                                    </div>
                                    <div class="country-item" data-country="FR">
                                        <img src="https://flagcdn.com/fr.svg" alt="France">
                                        <span>France</span>
                                    </div>
                                    <div class="country-item" data-country="IN">
                                        <img src="https://flagcdn.com/in.svg" alt="India">
                                        <span>India</span>
                                    </div>
                                    <div class="country-item" data-country="BR">
                                        <img src="https://flagcdn.com/br.svg" alt="Brazil">
                                        <span>Brazil</span>
                                    </div>
                                </div>
                                <div class="wizard-actions">
                                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button class="btn btn-primary" onclick="nextStep()">
                                        Next <i class="fas fa-arrow-right ms-2"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 3: Review Details -->
                            <div class="wizard-content d-none" id="wizard-step-3">
                                <h6 class="mb-3">
                                    <i class="fas fa-edit me-2"></i>Review Details
                                </h6>
                                <form id="reviewForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Business Name</label>
                                            <input type="text" class="form-control" name="business_name" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Business URL</label>
                                            <input type="url" class="form-control" name="business_url" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Number of Reviews</label>
                                            <select class="form-select" name="review_count" required>
                                                <option value="">Select count</option>
                                                <option value="1">1 Review</option>
                                                <option value="2">2 Reviews</option>
                                                <option value="3">3 Reviews</option>
                                                <option value="5">5 Reviews</option>
                                                <option value="10">10 Reviews</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Review Rating</label>
                                            <select class="form-select" name="rating" required>
                                                <option value="">Select rating</option>
                                                <option value="5">5 Stars</option>
                                                <option value="4">4 Stars</option>
                                                <option value="3">3 Stars</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Additional Notes (Optional)</label>
                                        <textarea class="form-control" name="notes" rows="3" placeholder="Any specific requirements or instructions..."></textarea>
                                    </div>
                                    <div class="wizard-actions">
                                        <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                                            <i class="fas fa-arrow-left me-2"></i>Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Next <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Step 4: Confirmation -->
                            <div class="wizard-content d-none" id="wizard-step-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-check-circle me-2"></i>Order Summary
                                </h6>
                                <div class="order-summary">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6><i class="fas fa-globe me-2"></i>Platform</h6>
                                                    <p id="summary-platform">-</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6><i class="fas fa-flag me-2"></i>Country</h6>
                                                    <p id="summary-country">-</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6><i class="fas fa-building me-2"></i>Business</h6>
                                                    <p id="summary-business">-</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6><i class="fas fa-star me-2"></i>Reviews</h6>
                                                    <p id="summary-reviews">-</p>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="text-center">
                                                <div class="total-amount" id="summary-total">$0.00</div>
                                                <p class="text-muted">Total Amount</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="wizard-actions">
                                    <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                                        <i class="fas fa-arrow-left me-2"></i>Previous
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="submitOrder()">
                                        <i class="fas fa-check me-2"></i>Confirm Order
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Existing Reviews -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>My Review Requests
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($reviews->count() > 0)
                                @foreach($reviews as $review)
                                    <div class="review-card">
                                        <div class="review-header">
                                            <h6>
                                                <i class="fab fa-{{ $review->platform }} me-2"></i>{{ ucfirst($review->platform) }}
                                            </h6>
                                            <span class="status-badge status-{{ $review->status }}">
                                                {{ ucfirst($review->status) }}
                                            </span>
                                        </div>
                                        <p class="mb-2"><strong>{{ $review->business_name }}</strong></p>
                                        <p class="text-muted mb-2">{{ $review->business_url }}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="text-muted">
                                                <i class="fas fa-star me-1"></i>{{ $review->review_count }} reviews
                                            </span>
                                            <span class="text-primary fw-bold">${{ number_format($review->total_amount, 2) }}</span>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ $review->created_at->format('M d, Y') }}
                                        </small>
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No review requests yet</p>
                                    <p class="text-muted">Start by posting your first review request</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Balance Modal -->
    <div class="modal fade" id="addBalanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-wallet me-2"></i>Add Balance
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form action="/add-balance" method="POST" id="addBalanceForm">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label">Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" name="amount" min="10" step="0.01" required>
                            </div>
                            <small class="text-muted">Minimum amount: $10.00</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Payment Method</label>
                            <select class="form-select" name="payment_method" required>
                                <option value="">Select payment method</option>
                                <option value="upi">UPI Payment</option>
                                <option value="bank">Bank Transfer</option>
                                <option value="crypto">Cryptocurrency</option>
                            </select>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-credit-card me-2"></i>Proceed to Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Details Modal -->
    <div class="modal fade" id="reviewDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye me-2"></i>Review Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="reviewDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Wizard functionality
        let currentStep = 1;
        let selectedPlatform = '';
        let selectedCountry = '';
        let orderData = {};

        // Platform selection
        document.querySelectorAll('.platform-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.platform-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedPlatform = this.dataset.platform;
            });
        });

        // Country selection
        document.querySelectorAll('.country-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.country-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
                selectedCountry = this.dataset.country;
            });
        });

        function nextStep() {
            if (currentStep === 1 && !selectedPlatform) {
                showAlert('Please select a platform', 'warning');
                return;
            }
            if (currentStep === 2 && !selectedCountry) {
                showAlert('Please select a country', 'warning');
                return;
            }
            if (currentStep === 3) {
                // Validate form
                const form = document.getElementById('reviewForm');
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }
                // Collect form data
                const formData = new FormData(form);
                orderData = {
                    platform: selectedPlatform,
                    country: selectedCountry,
                    business_name: formData.get('business_name'),
                    business_url: formData.get('business_url'),
                    review_count: formData.get('review_count'),
                    rating: formData.get('rating'),
                    notes: formData.get('notes')
                };
                updateSummary();
            }

            // Hide current step
            document.getElementById(`wizard-step-${currentStep}`).classList.add('d-none');
            
            // Show next step
            currentStep++;
            document.getElementById(`wizard-step-${currentStep}`).classList.remove('d-none');
            
            // Update progress
            updateProgress();
        }

        function prevStep() {
            // Hide current step
            document.getElementById(`wizard-step-${currentStep}`).classList.add('d-none');
            
            // Show previous step
            currentStep--;
            document.getElementById(`wizard-step-${currentStep}`).classList.remove('d-none');
            
            // Update progress
            updateProgress();
        }

        function updateProgress() {
            document.querySelectorAll('.wizard-step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        function updateSummary() {
            const platformNames = {
                'google': 'Google Reviews',
                'trustpilot': 'Trustpilot Reviews',
                'facebook': 'Facebook Reviews',
                'yelp': 'Yelp Reviews'
            };

            const countryNames = {
                'US': 'United States',
                'CA': 'Canada',
                'GB': 'United Kingdom',
                'AU': 'Australia',
                'DE': 'Germany',
                'FR': 'France',
                'IN': 'India',
                'BR': 'Brazil'
            };

            const platformPrices = {
                'google': 15,
                'trustpilot': 20,
                'facebook': 12,
                'yelp': 18
            };

            document.getElementById('summary-platform').textContent = platformNames[orderData.platform];
            document.getElementById('summary-country').textContent = countryNames[orderData.country];
            document.getElementById('summary-business').textContent = orderData.business_name;
            document.getElementById('summary-reviews').textContent = `${orderData.review_count} reviews (${orderData.rating} stars)`;

            const total = platformPrices[orderData.platform] * parseInt(orderData.review_count);
            document.getElementById('summary-total').textContent = `$${total.toFixed(2)}`;
        }

        function submitOrder() {
            // Show loading state
            const submitBtn = document.querySelector('#wizard-step-4 .btn-success');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Submit order via AJAX
            fetch('/submit-review-request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Review request submitted successfully!', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert(data.message || 'Error submitting review request', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error submitting review request', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        // Add Balance form submission
        document.getElementById('addBalanceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
            
            const formData = new FormData(this);
            
            fetch('/add-balance', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Balance request submitted successfully! Our team will process it within 24 hours.', 'success');
                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('addBalanceModal')).hide();
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert(data.message || 'Error submitting balance request', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error submitting balance request', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Review card click handler
        function viewReviewDetails(reviewId) {
            fetch(`/review-details/${reviewId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('reviewDetailsContent').innerHTML = data.html;
                        new bootstrap.Modal(document.getElementById('reviewDetailsModal')).show();
                    } else {
                        showAlert(data.message || 'Error loading review details', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error loading review details', 'error');
                });
        }

        // Custom alert function
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on load
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html> 
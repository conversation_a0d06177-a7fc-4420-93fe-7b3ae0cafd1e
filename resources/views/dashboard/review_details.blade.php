<div class="row">
    <div class="col-md-6">
        <h6>Platform</h6>
        <p class="mb-3">{{ ucfirst($review->platform) }}</p>
        
        <h6>Business Name</h6>
        <p class="mb-3">{{ $review->business_name }}</p>
        
        <h6>Business URL</h6>
        <p class="mb-3">
            <a href="{{ $review->business_url }}" target="_blank">{{ $review->business_url }}</a>
        </p>
        
        <h6>Country</h6>
        <p class="mb-3">{{ $review->country }}</p>
    </div>
    
    <div class="col-md-6">
        <h6>Review Count</h6>
        <p class="mb-3">{{ $review->review_count }} reviews</p>
        
        <h6>Rating</h6>
        <p class="mb-3">{{ $review->rating }} stars</p>
        
        <h6>Total Amount</h6>
        <p class="mb-3 text-primary fw-bold">${{ number_format($review->total_amount, 2) }}</p>
        
        <h6>Status</h6>
        <span class="status-badge status-{{ $review->status }}">
            {{ ucfirst($review->status) }}
        </span>
    </div>
</div>

@if($review->notes)
<div class="row mt-3">
    <div class="col-12">
        <h6>Additional Notes</h6>
        <p class="mb-0">{{ $review->notes }}</p>
    </div>
</div>
@endif

<div class="row mt-3">
    <div class="col-12">
        <h6>Created</h6>
        <p class="mb-0">{{ $review->created_at->format('F d, Y \a\t g:i A') }}</p>
    </div>
</div>

@if($review->updated_at != $review->created_at)
<div class="row mt-3">
    <div class="col-12">
        <h6>Last Updated</h6>
        <p class="mb-0">{{ $review->updated_at->format('F d, Y \a\t g:i A') }}</p>
    </div>
</div>
@endif 
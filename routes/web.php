<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PageController;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/services', [PageController::class, 'services'])->name('services');
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/privacy', [PageController::class, 'privacy'])->name('privacy');
Route::get('/terms', [PageController::class, 'terms'])->name('terms');

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Google OAuth Routes
Route::get('/auth/google', [AuthController::class, 'redirectToGoogle'])->name('google.login');
Route::get('/auth/google/callback', [AuthController::class, 'handleGoogleCallback'])->name('google.callback');

// Admin Authentication Routes
Route::get('/admin/login', [AuthController::class, 'showAdminLogin'])->name('admin.login');
Route::post('/admin/login', [AuthController::class, 'adminLogin']);
Route::get('/admin/forgot-password', [AuthController::class, 'showAdminForgotPassword'])->name('admin.forgot-password');
Route::post('/admin/forgot-password', [AuthController::class, 'adminForgotPassword']);

// Customer Dashboard Routes
Route::middleware(['auth', 'customer'])->group(function () {
    Route::get('/dashboard/customer', [DashboardController::class, 'customer'])->name('dashboard.customer');
    Route::post('/submit-review-request', [DashboardController::class, 'submitReviewRequest'])->name('submit.review.request');
    Route::post('/add-balance', [DashboardController::class, 'addBalance'])->name('add.balance');
    Route::get('/review-details/{id}', [DashboardController::class, 'reviewDetails'])->name('review.details');
});

// Reviewer Dashboard Routes
Route::middleware(['auth', 'reviewer'])->group(function () {
    Route::get('/dashboard/reviewer', [DashboardController::class, 'reviewer'])->name('dashboard.reviewer');
    Route::post('/claim-review/{id}', [DashboardController::class, 'claimReview'])->name('claim.review');
});

// Review Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/reviews/create', [ReviewController::class, 'create'])->name('reviews.create');
    Route::post('/reviews', [ReviewController::class, 'store'])->name('reviews.store');
    Route::get('/reviews/my-reviews', [ReviewController::class, 'myReviews'])->name('reviews.my-reviews');
    Route::get('/reviews/available', [ReviewController::class, 'available'])->name('reviews.available');
    Route::get('/reviews/claimed', [ReviewController::class, 'claimed'])->name('reviews.claimed');
    Route::get('/reviews/claimed-reviews', [ReviewController::class, 'claimedReviews'])->name('reviews.claimed-reviews');
});

// Wallet Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/wallet', [WalletController::class, 'index'])->name('wallet.index');
    Route::get('/wallet/withdrawals', [WalletController::class, 'withdrawals'])->name('wallet.withdrawals');
    Route::post('/wallet/withdraw', [WalletController::class, 'withdraw'])->name('wallet.withdraw');
});

// Admin Dashboard Routes
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin/dashboard', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    Route::get('/admin/review-details/{id}', [AdminController::class, 'reviewDetails'])->name('admin.review.details');
    Route::post('/admin/approve-review/{id}', [AdminController::class, 'approveReview'])->name('admin.approve.review');
    Route::post('/admin/reject-review/{id}', [AdminController::class, 'rejectReview'])->name('admin.reject.review');
    Route::get('/admin/settings', [AdminController::class, 'settings'])->name('admin.settings');
    Route::get('/admin/users', [AdminController::class, 'users'])->name('admin.users');
    Route::get('/admin/reviews', [AdminController::class, 'reviews'])->name('admin.reviews');
    Route::get('/admin/withdrawals', [AdminController::class, 'withdrawals'])->name('admin.withdrawals');
}); 
# 🎯 EXACT Google Cloud Console Setup for redirect_uri_mismatch

## 🚨 Current Error Analysis
```
Error 400: redirect_uri_mismatch
```

This error means Google is trying to redirect to a URI that's not in your authorized list.

## 🔍 Step-by-Step Google Cloud Console Fix

### Step 1: Access Your OAuth Client
1. Go to: https://console.cloud.google.com/
2. Navigate to: **APIs & Services** → **Credentials**
3. Find: `136943631858-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com`
4. Click the **pencil icon** to edit

### Step 2: Clear Existing Entries (if any)
- Remove any existing entries that might be incorrect
- Start fresh with the exact URLs below

### Step 3: Add Authorized JavaScript Origins
Click **+ ADD URI** in the "Authorized JavaScript origins" section and add:

```
http://127.0.0.1:8080
```

**That's it for origins - just one entry.**

### Step 4: Add Authorized Redirect URIs
Click **+ ADD URI** in the "Authorized redirect URIs" section and add these **one by one**:

```
http://127.0.0.1:8080
http://127.0.0.1:8080/
http://127.0.0.1:8080/auth/google/callback
http://127.0.0.1:8080/google-login
http://127.0.0.1:8080/oauth/callback
```

### Step 5: Save and Wait
1. Click **SAVE** at the bottom
2. Wait **10-15 minutes** for global propagation
3. **Do not test immediately** - Google needs time to update

## 🧪 Testing Instructions

### After 15 minutes:
1. **Clear browser cache completely** (Ctrl+Shift+Delete)
2. **Close and reopen browser**
3. Go to: http://127.0.0.1:8080/google-oauth-debug-simple.html
4. Try the Google Sign-In button

## 🔍 Alternative Debugging Method

If it still doesn't work, let's check what Google is actually requesting:

### Method 1: Browser Network Tab
1. Open Developer Tools (F12)
2. Go to **Network** tab
3. Click Google Sign-In button
4. Look for requests to `accounts.google.com`
5. Check the `redirect_uri` parameter in the request URL

### Method 2: Check Console Errors
1. Open Developer Tools (F12)
2. Go to **Console** tab
3. Look for specific error messages about redirect_uri

## 📋 Common redirect_uri_mismatch Causes

1. **Missing trailing slash**: `http://127.0.0.1:8080` vs `http://127.0.0.1:8080/`
2. **Wrong protocol**: `http` vs `https`
3. **Wrong port**: `8080` vs `3000` vs `8000`
4. **Case sensitivity**: exact match required
5. **Propagation delay**: Google changes take time

## 🚀 If Still Not Working

### Try this alternative Client ID setup:
1. Create a **new** OAuth 2.0 Client ID in Google Cloud Console
2. Set Application type to **Web application**
3. Add only these URIs:
   - **JavaScript origins**: `http://127.0.0.1:8080`
   - **Redirect URIs**: `http://127.0.0.1:8080`
4. Update your code with the new Client ID

## 📞 Emergency Fallback

If nothing works, the issue might be:
1. **Wrong Google Cloud Project**: Verify you're editing the correct project
2. **API not enabled**: Enable "Google+ API" or "Google Identity" in APIs & Services
3. **Quota exceeded**: Check quotas in Google Cloud Console
4. **Account restrictions**: Check if your Google account has restrictions

## ⚡ Quick Test URLs

After making changes, test these URLs:
- http://127.0.0.1:8080/google-oauth-debug-simple.html
- http://127.0.0.1:8080/google-oauth-test.html

The debug tool will show you exactly what URLs Google expects vs what you have configured.

## 🎯 Success Criteria

You'll know it's working when:
1. ✅ Google Sign-In button appears
2. ✅ Clicking it opens Google popup
3. ✅ No "redirect_uri_mismatch" error
4. ✅ Successful authentication callback

/* assets/styles.css */
/* Modern Black Color Theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
:root {
    --primary-bg: #fff;
    --primary: #000000;
    --primary-dark: #1a1a1a;
    --secondary: #f8f9fa;
    --accent: #6366f1;
    --text-main: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --bg-gray-50: #f9fafb;
    --border-light: #e5e7eb;
    --border-gray: #d1d5db;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --glass-bg: rgba(255,255,255,0.98);
    --glass-blur: blur(12px);
    --primary-black: #000000;
    --primary-yellow: #FFD700;
    --secondary-yellow: #FFA500;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    /* 1xreviews Brand Identity System */
    --brand-primary: #1877f2;      /* Facebook Blue */
    --brand-secondary: #42a5f5;    /* Light Blue */
    --brand-accent: #0d47a1;       /* Dark Blue */
    --brand-success: #00c851;      /* Green */
    --brand-warning: #ff9800;      /* Orange */
    --brand-danger: #f44336;       /* Red */
    --brand-info: #2196f3;         /* Info Blue */
    --brand-white: #ffffff;
    --brand-light-gray: #f8f9fa;
    --brand-dark-gray: #343a40;
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', sans-serif;
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    --radius-sm: 4px;
    --radius-md: 8px;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
    --shadow-lg: 0 10px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.15), 0 8px 20px rgba(0,0,0,0.1);
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    /* Neutral Colors */
    --brand-gray-50: #f8f9fa;
    --brand-gray-100: #f1f3f4;
    --brand-gray-200: #e8eaed;
    --brand-gray-300: #dadce0;
    --brand-gray-400: #bdc1c6;
    --brand-gray-500: #9aa0a6;
    --brand-gray-600: #80868b;
    --brand-gray-700: #5f6368;
    --brand-gray-800: #3c4043;
    --brand-gray-900: #202124;
    /* Light Colors */
    --brand-light-blue: #e3f2fd;
    --brand-light-green: #e8f5e8;
    --brand-light-orange: #fff3e0;
    --brand-light-red: #ffebee;
    /* Border Radius */
    --radius-full: 50px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-white) !important;
    color: var(--text-main);
    min-height: 100vh;
    line-height: 1.6;
}

/* Navigation Styles */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.95) !important;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.brand-icon {
    display: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.navbar-toggler {
    border: none !important;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 215, 0, 0.25) !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.15);
}

.navbar-nav .nav-link i {
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1);
}

/* Dropdown Styles */
.dropdown-menu {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.8) !important;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem;
}

.dropdown-item:hover {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.1) !important;
    transform: translateX(5px);
}

.dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Navbar Shrink Effect */
.navbar-shrink {
    padding: 0.5rem 0 !important;
    background: rgba(0, 0, 0, 0.98) !important;
}

.navbar-shrink .navbar-brand {
    font-size: 1.3rem;
}

.navbar-shrink .brand-icon {
    width: 35px;
    height: 35px;
}

/* Utility Classes */
.py-5 { padding-top: 3rem; padding-bottom: 3rem; }
.py-md-6 { padding-top: 4rem; padding-bottom: 4rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }
.text-gray-900 { color: var(--gray-900) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }
.bg-gray-50 { background-color: var(--gray-50) !important; }
.bg-gray-900 { background-color: var(--gray-900) !important; }
.border-gray-700 { border-color: var(--gray-700) !important; }
.hover\:text-white:hover { color: white !important; }

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.5) 100%);
    z-index: 2;
}

.hero-section > .container {
    position: relative;
    z-index: 3;
}

.hero-content {
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    padding-top: 2rem;
    padding-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Search Form Styles */
.hero-search-form {
    margin-bottom: 2rem;
}

.search-container {
    max-width: 500px;
    margin: 0 auto;
}

.search-input-group {
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.search-input {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    background: transparent;
    color: #333;
}

.search-input:focus {
    box-shadow: none;
    border: none;
    outline: none;
}

.search-input::placeholder {
    color: #666;
    opacity: 0.8;
}

.search-btn {
    border: none;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: linear-gradient(135deg, #0d47a1 0%, #1976d2 100%);
    transform: translateY(-1px);
}

/* Hero Tags Styles */
.hero-tags {
    margin-bottom: 2rem;
}

.hero-tag-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.hero-tag-btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Hero Actions Styles */
.hero-main-actions {
    margin-bottom: 2rem;
}

.hero-action-btn {
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* Trusted By Section */
.trusted-by {
    margin-top: 2rem;
}

.trusted-label {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.7;
}

.trusted-logo {
    opacity: 0.6;
    transition: all 0.3s ease;
}

.trusted-logo:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Mobile Responsive Styles */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3rem;
        padding-top: 1.5rem;
    }
}

@media (max-width: 992px) {
    .hero-section {
        min-height: 75vh;
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
        padding-top: 1rem;
        margin-bottom: 1rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .search-container {
        max-width: 450px;
    }
    
    .search-input {
        padding: 0.875rem 1.25rem;
    }
    
    .search-btn {
        padding: 0.875rem 1.25rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
        padding-top: 80px !important;
        padding-bottom: 0.5rem;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.25rem;
    }
    
    .hero-search-form {
        margin-bottom: 1.5rem;
    }
    
    .search-container {
        width: 100%;
        max-width: 320px;
    }
    
    .search-input-group {
        border-radius: 25px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .search-input {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .search-btn {
        padding: 0.75rem 1rem;
        border-radius: 0 25px 25px 0;
        font-size: 0.9rem;
    }
    
    .hero-tags {
        margin-bottom: 1.5rem;
    }
    
    .hero-tag-btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
    
    .hero-main-actions {
        margin-bottom: 1.5rem;
        flex-direction: column !important;
        gap: 1rem !important;
    }
    
    .hero-action-btn {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
        font-size: 0.9rem;
        padding: 0.875rem 1.5rem;
    }
    
    .trusted-by {
        display: none !important;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 60vh;
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
        padding-top: 80px !important;
        margin-bottom: 0.75rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .search-container {
        max-width: 280px;
    }
    
    .search-input {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
    
    .search-btn {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
    
    .hero-tag-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .hero-action-btn {
        font-size: 0.85rem;
        padding: 0.75rem 1rem;
        max-width: 260px;
    }
    
    .trusted-by {
        display: none !important;
    }
}

@media (max-width: 480px) {
    .hero-section {
        min-height: 55vh;
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
    
    .hero-title {
        font-size: 1.5rem;
        padding-top: 80px !important;
        margin-bottom: 0.5rem;
    }
    
    .hero-description {
        font-size: 0.85rem;
        margin-bottom: 0.75rem;
    }
    
    .search-container {
        max-width: 260px;
    }
    
    .search-input {
        font-size: 0.8rem;
        padding: 0.5rem 0.7rem;
    }
    
    .search-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.7rem;
    }
    
    .hero-tag-btn {
        font-size: 0.75rem;
        padding: 0.35rem 0.7rem;
    }
    
    .hero-action-btn {
        font-size: 0.8rem;
        padding: 0.6rem 0.8rem;
        max-width: 240px;
    }
    
    .trusted-by {
        display: none !important;
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation delays */
.hero-title.animate-fade-in-up {
    animation-delay: 0.1s;
}

.hero-search-form.animate-fade-in-up {
    animation-delay: 0.2s;
}

.hero-tags.animate-fade-in-up {
    animation-delay: 0.3s;
}

.hero-main-actions.animate-fade-in-up {
    animation-delay: 0.4s;
}

.trusted-by.animate-fade-in-up {
    animation-delay: 0.5s;
}

/* Hero Visual */
.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    top: 50%;
    right: -10%;
    animation-delay: 1s;
}

.floating-card.card-3 {
    bottom: 10%;
    left: 10%;
    animation-delay: 2s;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.card-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brand-success);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--brand-gray-900) 0%, #000 100%);
}

.footer-brand .brand-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.footer-brand .logo-icon {
    width: 40px;
    height: 40px;
    background: var(--brand-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.footer-brand .logo-text {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.footer-heading {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.footer-heading i {
    color: var(--brand-primary);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a:hover {
    color: var(--brand-primary);
    transform: translateX(5px);
}

.footer-links a i {
    font-size: 0.7rem;
    color: var(--brand-primary);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--brand-primary);
    color: white;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255,255,255,0.1);
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-bottom-link {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-link:hover {
    color: var(--brand-primary);
}

.footer-copyright {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        padding: 1.5rem;
    }
    
    .hero-stats .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .hero-stats .stat-number {
        font-size: 1.5rem;
    }
    
    .trust-badges {
        justify-content: center;
    }
    
    .floating-card {
        position: relative;
        margin-bottom: 1rem;
        animation: none;
    }
    
    .footer-bottom-links {
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .footer-copyright {
        text-align: center;
    }
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, var(--brand-gray-50) 0%, white 100%);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(24, 119, 242, 0.1);
    color: var(--brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    line-height: 1.2;
}

.section-description {
    font-size: 1.1rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.service-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 0.75rem;
}

.service-description {
    color: var(--brand-gray-600);
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    background: var(--brand-light-green);
    color: var(--brand-success);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Coming Soon Section */
.coming-soon-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    border: 2px dashed var(--brand-gray-300);
}

.coming-soon-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    text-align: center;
}

.coming-soon-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 12px;
    background: var(--brand-gray-50);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.coming-soon-card:hover {
    background: var(--brand-light-blue);
    transform: translateY(-2px);
}

.coming-soon-icon {
    width: 60px;
    height: 60px;
    background: var(--brand-gray-300);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--brand-gray-600);
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    position: relative;
}

.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.coming-soon-card h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--brand-gray-700);
    margin-bottom: 0.5rem;
}

.coming-soon-description {
    font-size: 0.85rem;
    color: var(--brand-gray-500);
    margin: 0;
}

/* How It Works Section */
.how-it-works-section {
    background: linear-gradient(135deg, white 0%, var(--brand-light-blue) 100%);
}

.step-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.step-number {
    position: absolute;
    top: -10px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: var(--brand-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.step-card:hover .step-icon {
    transform: scale(1.1);
}

.step-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 1rem;
}

.step-description {
    color: var(--brand-gray-600);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.step-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.step-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--brand-gray-700);
    font-size: 0.9rem;
}

.step-feature i {
    color: var(--brand-success);
    font-size: 1rem;
}

/* Stats Showcase */
.stats-showcase {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    margin-top: 3rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--brand-gray-50);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: var(--brand-light-blue);
    transform: translateY(-2px);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--brand-gray-600);
    font-weight: 600;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    margin-top: 3rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-gray-900);
}

.cta-description {
    font-size: 1.1rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.cta-buttons .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Benefits Section */
.benefits-section {
    background: linear-gradient(135deg, white 0%, var(--brand-gray-50) 100%);
}

.benefits-visual {
    position: relative;
}

.benefits-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.benefits-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

.floating-benefit-stats {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.benefit-stat-bubble {
    position: absolute;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
}

.benefit-stat-bubble:nth-child(1) {
    top: 10%;
    right: -20px;
    animation-delay: 0s;
}

.benefit-stat-bubble:nth-child(2) {
    bottom: 10%;
    left: -20px;
    animation-delay: 1.5s;
}

.benefit-stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.benefit-stat-content {
    flex: 1;
}

.benefit-stat-number {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.benefit-stat-label {
    font-size: 0.8rem;
    color: var(--brand-gray-600);
    font-weight: 500;
}

.benefit-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--brand-primary);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
}

.benefit-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 1rem;
}

.benefit-description {
    color: var(--brand-gray-600);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
}

.trust-indicators {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--brand-gray-200);
}

.trust-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.trust-title i {
    color: var(--brand-primary);
}

.trust-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.trust-badges .trust-badge {
    display: flex;
    align-items: center;
    background: var(--brand-light-green);
    color: var(--brand-success);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
}

.trust-badges .trust-badge i {
    font-size: 0.8rem;
}

/* FAQ Section */
.faq-section {
    background: linear-gradient(135deg, var(--brand-gray-50) 0%, white 100%);
}

.faq-container {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.faq-item {
    border: 1px solid var(--brand-gray-200);
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--brand-primary);
    box-shadow: var(--shadow-md);
}

.faq-item:last-child {
    margin-bottom: 0;
}

.faq-header {
    background: white;
}

.faq-button {
    width: 100%;
    padding: 1.5rem;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-button:hover {
    background: var(--brand-gray-50);
}

.faq-button:focus {
    outline: none;
    box-shadow: none;
}

.faq-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.faq-content {
    flex: 1;
}

.faq-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 0.5rem;
}

.faq-preview {
    font-size: 0.9rem;
    color: var(--brand-gray-600);
    margin: 0;
}

.faq-arrow {
    width: 30px;
    height: 30px;
    background: var(--brand-gray-100);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--brand-gray-600);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.faq-button[aria-expanded="true"] .faq-arrow {
    transform: rotate(180deg);
    background: var(--brand-primary);
    color: white;
}

.faq-body {
    padding: 0 1.5rem 1.5rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.faq-body p {
    margin: 0;
    font-size: 0.95rem;
}

/* Platform Icon Cards */
.platform-icon-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.platform-icon-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--brand-primary);
}

.platform-icon-small {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all var(--transition-normal);
    position: relative;
}

.platform-icon-small i {
    font-size: 1.5rem;
    color: white;
    transition: all var(--transition-normal);
}

.platform-icon-card:hover .platform-icon-small {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(24, 119, 242, 0.3);
}

.platform-icon-card:hover .platform-icon-small i {
    transform: scale(1.1);
}

/* Coming Soon Icon Cards */
.coming-soon-icon-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 2px solid var(--brand-gray-200);
    position: relative;
    overflow: hidden;
}

.coming-soon-icon-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.coming-soon-icon-small {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-gray-300), var(--brand-gray-400));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: all var(--transition-normal);
    position: relative;
}

.coming-soon-icon-small i {
    font-size: 1.5rem;
    color: var(--brand-gray-600);
    transition: all var(--transition-normal);
}

.lock-overlay-small {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
}

.lock-overlay-small i {
    color: white;
    font-size: 1rem;
}

.coming-soon-icon-card:hover .lock-overlay-small {
    opacity: 1;
}

.coming-soon-icon-card:hover .coming-soon-icon-small {
    transform: scale(1.05);
}

/* Responsive adjustments for platform icons */
@media (max-width: 768px) {
    .platform-icon-small,
    .coming-soon-icon-small {
        width: 50px;
        height: 50px;
    }
    
    .platform-icon-small i,
    .coming-soon-icon-small i {
        font-size: 1.25rem;
    }
    
    .platform-icon-card,
    .coming-soon-icon-card {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .platform-icon-small,
    .coming-soon-icon-small {
        width: 45px;
        height: 45px;
    }
    
    .platform-icon-small i,
    .coming-soon-icon-small i {
        font-size: 1.1rem;
    }
}

/* Hero Video Background */
.hero-bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: -1;
}

.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: #fff;
}

.hero-title.display-2 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: #fff;
}

.hero-search-form {
    max-width: 700px;
    margin: 0 auto 1.5rem auto;
    background: rgba(255,255,255,0.1);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.12);
}

.hero-search-input {
    border: none;
    background: transparent;
    color: #fff;
    font-size: 1.25rem;
    border-radius: 50px;
    outline: none;
}

.hero-search-input::placeholder {
    color: #eee;
    opacity: 1;
}

.hero-search-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-size: 1.25rem;
}

.hero-tags {
    gap: 1rem;
}

.hero-tags .btn {
    border: 1.5px solid #fff;
    color: #fff;
    background: rgba(255,255,255,0.08);
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.2s;
}

.hero-tags .btn:hover {
    background: #fff;
    color: #222;
}

.trusted-by {
    gap: 2rem;
    opacity: 0.85;
    margin-top: 2rem;
}

.trusted-label {
    font-size: 1rem;
    color: #fff;
    opacity: 0.7;
}

.trusted-logo {
    height: 28px;
    opacity: 0.85;
    filter: grayscale(1) brightness(1.2);
    margin-right: 0.5rem;
}

@media (max-width: 768px) {
    .hero-title.display-2 {
        font-size: 2.2rem;
    }
    .hero-search-form {
        flex-direction: column;
        padding: 0.5rem;
    }
    .hero-search-btn {
        width: 100%;
        margin-top: 0.5rem;
    }
    .hero-tags {
        gap: 0.5rem;
    }
    .trusted-by {
        gap: 1rem;
    }
}

/* =================================
   REDESIGNED HOMEPAGE SECTIONS
   ================================= */

/* Minimalistic Reviews Completed Section */
.reviews-completed-section {
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    margin: 0 auto;
}

.completed-icon {
    font-size: 3rem;
    color: #28a745;
}

.completed-counter {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1f2937;
    margin: 0.5rem 0;
}

.completed-label {
    font-size: 1.1rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    width: 0%;
    animation: fillProgress 2s ease-out forwards;
}

@keyframes fillProgress {
    to { width: 75%; }
}

/* Modern Platform Cards - App-like Design */
.platform-card-modern {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    height: 100%;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.platform-card-modern:hover {
    transform: scale(1.02); /* hover:scale-105 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
    border-color: #3b82f6;
}

.platform-logo-modern {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.platform-logo-modern img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.platform-name-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

/* Service Type Selection */
.service-type-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    height: 100%;
}

.service-type-card:hover,
.service-type-card.active {
    border-color: #007bff;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.service-type-card .service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.service-type-card h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.service-type-card p {
    color: #6b7280;
    margin: 0;
    font-size: 0.9rem;
}

/* Country Flags */
.country-flags {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.flag-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    border: 2px solid transparent;
    min-width: 100px;
}

.flag-option:hover,
.flag-option.active {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.flag-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.flag-option span {
    font-size: 0.9rem;
    font-weight: 500;
    color: #1f2937;
}

/* TikTok Services */
.tiktok-service-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.tiktok-logo-container {
    margin-bottom: 2rem;
}

.tiktok-logo {
    width: 80px;
    height: auto;
}

.service-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.service-description {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.service-features-grid .feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: 500;
    color: #1f2937;
}

.service-features-grid .feature-item i {
    color: #007bff;
    font-size: 1.1rem;
}

.btn-tiktok {
    background: linear-gradient(135deg, #ff0050, #ff4081);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-tiktok:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 80, 0.3);
    color: white;
}

/* How to Start Steps */
.start-step-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.start-step-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.step-number-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
}

.step-icon-container {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem auto 1.5rem;
    font-size: 1.8rem;
    color: #007bff;
    transition: all 0.3s ease;
}

.start-step-card:hover .step-icon-container {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: scale(1.1);
}

.step-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.step-description {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

/* Counter Animation */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.completed-counter {
    animation: countUp 1s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .platform-card {
        padding: 1.5rem 1rem;
    }
    
    .platform-logo {
        width: 48px;
        height: 48px;
        margin-bottom: 1rem;
    }
    
    .service-features-grid {
        grid-template-columns: 1fr;
    }
    
    .country-flags {
        gap: 0.5rem;
    }
    
    .flag-option {
        min-width: 80px;
        padding: 0.75rem;
    }
    
    .flag-icon {
        font-size: 2rem;
    }
    
    .start-step-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .step-icon-container {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .completed-counter {
        font-size: 2.5rem;
    }
    
    .reviews-completed-section {
        padding: 1.5rem;
    }
    
    .tiktok-service-card {
        padding: 2rem 1.5rem;
    }
    
    .service-title {
        font-size: 1.5rem;
    }
}

/* Modern Opportunity Cards - App-like Design */
.opportunity-card-modern {
    background: white;
    border-radius: 12px; /* rounded-xl */
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.opportunity-card-modern:hover {
    transform: scale(1.02); /* hover:scale-105 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.opportunity-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.25rem;
    border-bottom: 1px solid #f3f4f6;
}

.business-info-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.business-name-modern {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem;
}

.platform-badge-modern {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.platform-badge-modern i {
    font-size: 0.875rem;
}

.reward-badge-modern {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
}

.opportunity-meta-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.time-badge-modern,
.claimed-badge-modern {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.lock-overlay-modern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.opportunity-card-modern:hover .lock-overlay-modern {
    opacity: 1;
}

.lock-content-modern {
    text-align: center;
    color: white;
}

.lock-content-modern i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.lock-content-modern span {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Modern Service Cards - App-like Design */
.service-card-modern {
    background: white;
    padding: 1.5rem; /* p-6 */
    border-radius: 12px; /* rounded-xl */
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.service-card-modern:hover {
    transform: scale(1.05); /* hover:scale-105 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-xl */
    border-color: #3b82f6;
    background-color: #f8fafc; /* slight background change */
}

.service-icon-modern {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 1.5rem;
    color: #007bff;
}

.service-icon-modern img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.service-title-modern {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.service-desc-modern {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
    margin: 0;
}

/* Responsive Updates */
@media (max-width: 768px) {
    .platform-card-modern {
        padding: 1rem;
    }
    
    .platform-logo-modern {
        width: 40px;
        height: 40px;
    }
    
    .opportunity-header-modern {
        padding: 1rem;
    }
    
    .business-name-modern {
        font-size: 0.9rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 0.25rem;
    }
    
    .service-card-modern {
        padding: 1.25rem;
    }
    
    .service-icon-modern {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}

/* Modern Navbar Styling */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.brand-logo-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #1f2937;
    font-weight: 700;
    font-size: 1.25rem;
}

.brand-icon-modern {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.brand-text-modern {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link-modern {
    color: #6b7280 !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-link-modern:hover, .nav-link-modern.active {
    color: #3b82f6 !important;
    background-color: #f8fafc;
}

.nav-link-modern.btn-login {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white !important;
    border-radius: 8px;
    padding: 0.5rem 1.25rem !important;
}

.nav-link-modern.btn-login:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    color: white !important;
}

.dropdown-menu-modern {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item-modern {
    color: #6b7280;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.dropdown-item-modern:hover {
    color: #3b82f6;
    background-color: #f8fafc;
}

.user-avatar-modern {
    width: 24px;
    height: 24px;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Why Choose Us - Minimal Design */
.why-choose-content-modern {
    padding: 2rem 0;
}

.benefit-card-minimal {
    text-align: center;
    padding: 2rem 1rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    height: 100%;
}

.benefit-card-minimal:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.benefit-icon-minimal {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    background: #f8fafc;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #3b82f6;
}

.benefit-title-minimal {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.benefit-desc-minimal {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

.trust-indicators-minimal {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #f3f4f6;
}

.trust-badges-minimal {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.trust-badge-minimal {
    display: flex;
    align-items: center;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.trust-badge-minimal i {
    color: #10b981;
}

/* Additional Mobile Responsive Updates */
@media (max-width: 768px) {
    .navbar-modern {
        padding: 0.5rem 0;
    }
    
    .brand-logo-modern {
        font-size: 1.125rem;
    }
    
    .brand-icon-modern {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }
    
    .benefit-card-minimal {
        padding: 1.5rem 1rem;
    }
    
    .benefit-icon-minimal {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
    
    .trust-badges-minimal {
        gap: 1rem;
    }
    
    .trust-badge-minimal {
        font-size: 0.8rem;
    }
}

/* Modern Navbar Styling */
#main-nav {
  background: linear-gradient(135deg, #0057FF 0%, #007BFF 100%);
  min-height: 64px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 0.75rem 0;
}

#main-nav .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#main-nav .navbar-brand {
  font-size: 1.5rem;
  letter-spacing: -0.5px;
  color: white !important;
  text-decoration: none;
  display: flex;
  align-items: center;
}

#main-nav .brand-icon {
  width: 32px;
  height: 32px;
  filter: brightness(0) invert(1);
  flex-shrink: 0;
}

#main-nav .brand-text {
  color: white;
  font-weight: 700;
  margin-left: 0.5rem;
}

#main-nav .navbar-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#main-nav .nav-link {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85) !important;
  padding: 0.75rem 1rem;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

#main-nav .nav-link:hover,
#main-nav .nav-link.active {
  color: white !important;
  transform: translateY(-1px);
}

#main-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

#main-nav .nav-link:hover::after,
#main-nav .nav-link.active::after {
  width: 60%;
}

#main-nav .dropdown-toggle::after {
  display: none;
}

#main-nav .dropdown-menu {
  background: white;
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
  padding: 0.5rem 0;
  min-width: 160px;
}

#main-nav .dropdown-item {
  color: #374151;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

#main-nav .dropdown-item:hover {
  background-color: #f3f4f6;
  color: #0057FF;
  transform: translateX(4px);
}

#main-nav .btn {
  font-weight: 600;
  border-width: 2px;
  padding: 0.625rem 1.25rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

#main-nav .btn-light {
  color: #0057FF;
  background-color: white;
  border-color: white;
}

#main-nav .btn-light:hover {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #0057FF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#main-nav .btn-outline-light {
  color: white;
  border-color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
}

#main-nav .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

#main-nav .navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

#main-nav .navbar-toggler:focus {
  box-shadow: none;
  outline: none;
}

#main-nav .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
  #main-nav .navbar-collapse {
    background: linear-gradient(135deg, #0057FF 0%, #007BFF 100%);
    margin: 1rem -1rem -1rem -1rem;
    padding: 1rem;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  #main-nav .navbar-nav {
    margin-bottom: 1rem;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  #main-nav .nav-link {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
  }
  
  #main-nav .nav-link:last-child {
    border-bottom: none;
  }
  
  #main-nav .nav-link::after {
    display: none;
  }
  
  #main-nav .dropdown-menu {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 0;
    margin-left: 1rem;
    position: static !important;
    transform: none !important;
    width: calc(100% - 1rem);
  }
  
  #main-nav .dropdown-item {
    color: white;
  }
  
  #main-nav .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  #main-nav .d-flex {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }
  
  #main-nav .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  #main-nav {
    padding: 0.5rem 0;
  }
  
  #main-nav .navbar-brand {
    font-size: 1.25rem;
  }
  
  #main-nav .brand-icon {
    width: 28px;
    height: 28px;
  }
  
  #main-nav .brand-text {
    margin-left: 0.375rem;
  }
  
  #main-nav .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

.modern-top-nav {
    background: transparent;
    z-index: 1051;
    min-height: 60px;
    transition: background 0.3s;
    padding: 0.75rem 1.5rem;
    margin: 0 auto;
    max-width: 100vw;
    width: 100%;
    box-sizing: border-box;
    display: flex !important;
    align-items: center;
    justify-content: center;
}
.modern-top-nav.scrolled {
    background: #111 !important;
}
.modern-top-nav > .d-flex.align-items-center {
    justify-content: center;
    width: 100%;
}
.modern-top-nav .nav-link-minimal,
.modern-top-nav .dropdown-toggle {
    padding: 0.75rem 1.25rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    text-align: center;
}
.modern-top-nav .dropdown-menu {
    min-width: 180px;
}
.modern-top-nav .btn {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}
@media (max-width: 991.98px) {
    .modern-top-nav {
        padding: 0.5rem 0.5rem;
    }
    .modern-top-nav .nav-link-minimal,
    .modern-top-nav .dropdown-toggle {
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }
    .modern-top-nav .btn {
        padding: 0.5rem 1rem;
        font-size: 0.95rem;
    }
}
@media (max-width: 767.98px) {
    .modern-top-nav {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 0.25rem;
        min-height: 56px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100vw;
        margin: 0;
        border-radius: 0;
    }
    .modern-top-nav > .d-flex.align-items-center {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        justify-content: center;
        align-items: center;
    }
    .modern-top-nav .nav-link-minimal,
    .modern-top-nav .dropdown-toggle {
        width: 100%;
        margin: 0.15rem 0;
        padding: 0.5rem 0.5rem;
        text-align: center;
    }
    .modern-top-nav .dropdown-menu {
        left: 50% !important;
        transform: translateX(-50%) !important;
        min-width: 160px;
    }
    .modern-top-nav .btn {
        width: 100%;
        margin: 0.25rem 0;
        padding: 0.5rem 0.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 991.98px) {
  .navbar-nav .nav-link {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem;
  }
  .navbar-brand {
    font-size: 1.2rem;
  }
  .dropdown-menu {
    width: 100%;
    left: 0;
    right: 0;
  }
}

@media (max-width: 767.98px) {
  .navbar {
    padding: 0.5rem 1rem;
  }
  .navbar-nav {
    flex-direction: column;
    align-items: flex-start;
  }
  .navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    width: 100%;
    text-align: left;
  }
  .navbar-toggler {
    margin-left: auto;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1rem;
  }
  .navbar-nav .nav-link {
    font-size: 0.85rem;
  }
  .dropdown-menu {
    font-size: 0.85rem;
  }
}
<?php
// includes/wallet.php - Wallet and Currency Management System

require_once 'db.php';

// Global cache for settings
$__ADMIN_SETTINGS_CACHE = null;

/**
 * Get admin setting value
 */
function get_setting($key, $default = null) {
    global $pdo, $__ADMIN_SETTINGS_CACHE;
    // Load all settings once per page load
    if ($__ADMIN_SETTINGS_CACHE === null) {
        $__ADMIN_SETTINGS_CACHE = [];
        try {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM admin_settings");
            foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $row) {
                $__ADMIN_SETTINGS_CACHE[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            error_log("Error loading admin settings: " . $e->getMessage());
        }
    }
    return array_key_exists($key, $__ADMIN_SETTINGS_CACHE) ? $__ADMIN_SETTINGS_CACHE[$key] : $default;
}

/**
 * Update admin setting
 */
function update_setting($key, $value) {
    error_log('DEBUG: Entered update_setting');
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$key, $value]);
    } catch (Exception $e) {
        error_log("Error updating setting $key: " . $e->getMessage());
        return false;
    }
}

/**
 * Get current currency settings
 */
function get_currency_settings() {
    error_log('DEBUG: Entered get_currency_settings');
    return [
        'currency' => get_setting('currency', 'INR'),
        'symbol' => get_setting('currency_symbol', '₹'),
        'signup_bonus_customer' => floatval(get_setting('signup_bonus_customer', '10.00')),
        'signup_bonus_reviewer' => floatval(get_setting('signup_bonus_reviewer', '0.00')),
        'minimum_payout' => floatval(get_setting('minimum_payout', '100.00')),
        'platform_fee_percentage' => floatval(get_setting('platform_fee_percentage', '10.00'))
    ];
}

/**
 * Format currency amount
 */
function format_currency($amount, $show_symbol = true) {
    error_log('DEBUG: Entered format_currency');
    $settings = get_currency_settings();
    $formatted = number_format($amount, 2);
    return $show_symbol ? $settings['symbol'] . $formatted : $formatted;
}

/**
 * Add signup bonus to new user
 */
function give_signup_bonus($user_id, $role) {
    error_log('DEBUG: Entered give_signup_bonus');
    global $pdo;
    
    try {
        // Check if bonus already given
        $stmt = $pdo->prepare("SELECT signup_bonus_given FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if ($user && $user['signup_bonus_given']) {
            return true; // Already given
        }
        
        $settings = get_currency_settings();
        $bonus_amount = ($role === 'customer') ? $settings['signup_bonus_customer'] : $settings['signup_bonus_reviewer'];
        
        if ($bonus_amount > 0) {
            $pdo->beginTransaction();
            
            // Add bonus to wallet
            $stmt = $pdo->prepare("UPDATE users SET wallet_balance = wallet_balance + ?, signup_bonus_given = 1 WHERE id = ?");
            $stmt->execute([$bonus_amount, $user_id]);
            
            // Record transaction
            $stmt = $pdo->prepare("
                INSERT INTO wallet_transactions (user_id, type, amount, reference, description) 
                VALUES (?, 'signup_bonus', ?, 'signup_bonus', ?)
            ");
            $description = "Signup bonus for new " . ucfirst($role);
            $stmt->execute([$user_id, $bonus_amount, $description]);
            
            $pdo->commit();
            return true;
        }
        
        // Mark as given even if amount is 0
        $stmt = $pdo->prepare("UPDATE users SET signup_bonus_given = 1 WHERE id = ?");
        $stmt->execute([$user_id]);
        
        return true;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        error_log("Error giving signup bonus: " . $e->getMessage());
        return false;
    }
}

/**
 * Add funds to user wallet
 */
function add_wallet_funds($user_id, $amount, $type = 'topup', $reference = '', $description = '', $review_id = null) {
    error_log('DEBUG: Entered add_wallet_funds');
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Update wallet balance
        $stmt = $pdo->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);
        
        // Record transaction
        $stmt = $pdo->prepare("
            INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference, description) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $type, $amount, $review_id, $reference, $description]);
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error adding wallet funds: " . $e->getMessage());
        return false;
    }
}

/**
 * Deduct funds from user wallet
 */
function deduct_wallet_funds($user_id, $amount, $type = 'debit', $reference = '', $description = '', $review_id = null) {
    error_log('DEBUG: Entered deduct_wallet_funds');
    global $pdo;
    
    try {
        // Check if user has sufficient balance
        $stmt = $pdo->prepare("SELECT wallet_balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user || $user['wallet_balance'] < $amount) {
            return false; // Insufficient balance
        }
        
        $pdo->beginTransaction();
        
        // Update wallet balance
        $stmt = $pdo->prepare("UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?");
        $stmt->execute([$amount, $user_id]);
        
        // Record transaction
        $stmt = $pdo->prepare("
            INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference, description) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $type, $amount, $review_id, $reference, $description]);
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error deducting wallet funds: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user's wallet balance
 */
function get_wallet_balance($user_id) {
    error_log('DEBUG: Entered get_wallet_balance');
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT wallet_balance FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        return $user ? floatval($user['wallet_balance']) : 0.00;
    } catch (Exception $e) {
        error_log("Error getting wallet balance: " . $e->getMessage());
        return 0.00;
    }
}

/**
 * Get wallet transaction history
 */
function get_wallet_transactions($user_id, $limit = 50) {
    error_log('DEBUG: Entered get_wallet_transactions');
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT wt.*, r.business_name, r.platform 
            FROM wallet_transactions wt 
            LEFT JOIN reviews r ON wt.review_id = r.id 
            WHERE wt.user_id = ? 
            ORDER BY wt.timestamp DESC 
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error getting wallet transactions: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if withdrawal amount meets minimum requirement
 */
function can_withdraw($amount) {
    error_log('DEBUG: Entered can_withdraw');
    $settings = get_currency_settings();
    return $amount >= $settings['minimum_payout'];
}

/**
 * Process review payment to reviewer
 */
function pay_reviewer($review_id, $reviewer_id, $amount) {
    error_log('DEBUG: Entered pay_reviewer');
    global $pdo;
    
    try {
        $settings = get_currency_settings();
        $platform_fee = ($amount * $settings['platform_fee_percentage']) / 100;
        $reviewer_amount = $amount - $platform_fee;
        
        $pdo->beginTransaction();
        
        // Add payment to reviewer
        $stmt = $pdo->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
        $stmt->execute([$reviewer_amount, $reviewer_id]);
        
        // Record transaction
        $stmt = $pdo->prepare("
            INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference, description) 
            VALUES (?, 'review_payment', ?, ?, 'review_completed', ?)
        ");
        $description = "Payment for completed review (Platform fee: " . format_currency($platform_fee) . ")";
        $stmt->execute([$reviewer_id, $reviewer_amount, $review_id, $description]);
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Error processing reviewer payment: " . $e->getMessage());
        return false;
    }
}

/**
 * Get available currency options
 */
function get_currency_options() {
    error_log('DEBUG: Entered get_currency_options');
    return [
        'USD' => ['symbol' => '$', 'name' => 'US Dollar'],
        'EUR' => ['symbol' => '€', 'name' => 'Euro'],
        'GBP' => ['symbol' => '£', 'name' => 'British Pound'],
        'INR' => ['symbol' => '₹', 'name' => 'Indian Rupee'],
        'CAD' => ['symbol' => 'C$', 'name' => 'Canadian Dollar'],
        'AUD' => ['symbol' => 'A$', 'name' => 'Australian Dollar'],
        'JPY' => ['symbol' => '¥', 'name' => 'Japanese Yen'],
        'CNY' => ['symbol' => '¥', 'name' => 'Chinese Yuan'],
        'SEK' => ['symbol' => 'kr', 'name' => 'Swedish Krona'],
        'NOK' => ['symbol' => 'kr', 'name' => 'Norwegian Krone'],
        'DKK' => ['symbol' => 'kr', 'name' => 'Danish Krone'],
        'CHF' => ['symbol' => 'Fr', 'name' => 'Swiss Franc'],
        'SGD' => ['symbol' => 'S$', 'name' => 'Singapore Dollar'],
        'HKD' => ['symbol' => 'HK$', 'name' => 'Hong Kong Dollar'],
        'NZD' => ['symbol' => 'NZ$', 'name' => 'New Zealand Dollar']
    ];
}
?> 
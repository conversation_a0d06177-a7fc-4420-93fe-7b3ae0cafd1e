<!-- Supported Platforms Section -->
<section class="platforms-section py-5 py-md-6" id="platforms">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-badge mb-3">
                    <i class="fas fa-th-large me-2"></i>
                    <span>Supported Platforms</span>
                </div>
                <h2 class="section-title mb-4">
                    Review on Your Favorite 
                    <span class="text-primary">Platforms</span>
                </h2>
                <p class="section-description">
                    Choose from a wide range of popular review platforms and start earning money for your honest feedback.
                </p>
            </div>
        </div>
        
        <div class="row g-4 justify-content-center">
            <!-- Google Reviews - Always visible -->
            <div class="col-lg-3 col-md-4 col-4">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="32" fill="#4285F4"/>
                            <path d="M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm0 44c-11.046 0-20-8.954-20-20S20.954 12 32 12s20 8.954 20 20-8.954 20-20 20z" fill="white"/>
                            <path d="M32 16c-8.837 0-16 7.163-16 16s7.163 16 16 16 16-7.163 16-16-7.163-16-16-16zm0 28c-6.627 0-12-5.373-12-12s5.373-12 12-12 12 5.373 12 12-5.373 12-12 12z" fill="white"/>
                            <path d="M32 24c-4.418 0-8 3.582-8 8s3.582 8 8 8 8-3.582 8-8-3.582-8-8-8zm0 12c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4z" fill="white"/>
                        </svg>
                    </div>
                    <h5 class="platform-name-modern">Google Reviews</h5>
                </div>
            </div>
            
            <!-- Trustpilot - Always visible -->
            <div class="col-lg-3 col-md-4 col-4">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://cdn.trustpilot.net/brand-assets/4.1.0/logo-white.svg" alt="Trustpilot" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMwMEM5NEYiLz4KPHN0YXIgY3g9IjI0IiBjeT0iMjQiIHI9IjEwIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">Trustpilot</h5>
                </div>
            </div>
            
            <!-- TripAdvisor - Always visible -->
            <div class="col-lg-3 col-md-4 col-4">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://static.tacdn.com/img2/brand_refresh/Tripadvisor_lockup_horizontal_secondary_registered.svg" alt="TripAdvisor" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMwMEM5NEYiLz4KPHN0YXIgY3g9IjI0IiBjeT0iMjQiIHI9IjEwIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">TripAdvisor</h5>
                </div>
            </div>
            
            <!-- YouTube - Hidden on mobile -->
            <div class="col-lg-3 col-md-4 col-sm-6 d-none d-md-block">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg" alt="YouTube">
                    </div>
                    <h5 class="platform-name-modern">YouTube</h5>
                </div>
            </div>
            
            <!-- Amazon - Hidden on mobile -->
            <div class="col-lg-3 col-md-4 col-sm-6 d-none d-md-block">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/200px-Amazon_logo.svg.png" alt="Amazon">
                    </div>
                    <h5 class="platform-name-modern">Amazon</h5>
                </div>
            </div>
            
            <!-- Facebook - Hidden on mobile -->
            <div class="col-lg-3 col-md-4 col-sm-6 d-none d-md-block">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/en/0/04/Facebook_f_logo_%282021%29.svg" alt="Facebook" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiByeD0iMTIiIGZpbGw9IiMxODc3RjIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNOS4xMDEgMjMuNjkxVjEzLjY0N0g2LjY5M1Y5LjkyNEg5LjEwMVY3LjI1NEM5LjEwMSA0Ljc5NSAxMC43MDQgMy4zMDkgMTMuNzggMy4zMDlDMTUuNjY3IDMuMzA5IDE2LjI0OCAzLjUxOSAxNi4yNDggMy41MTlMMTUuNjMzIDYuOTU3Qzk1LjY0IDYuOTU3IDE0Ljk2OSA2Ljk1NyAxNC45NjkgNi45NTdDMTQuMjkzIDYuOTU3IDEzLjY0NSA2Ljk1NyAxMy42NDUgOC4xODVWOS45MjRIMTUuNjU3QzE1LjU1NSAxMC42MjEgMTUuNDAzIDEyLjI1MiAxNS4zIDEzLjY0N0gxMy42NDVWMjMuNjkxSDE1LjY1N1Y5LjkyNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">Facebook</h5>
                </div>
            </div>
            
            <!-- TikTok - Hidden on mobile -->
            <div class="col-lg-3 col-md-4 col-sm-6 d-none d-md-block">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/en/a/a9/TikTok_logo.svg" alt="TikTok">
                    </div>
                    <h5 class="platform-name-modern">TikTok</h5>
                </div>
            </div>
        </div>
        
        <!-- Mobile "View More" button -->
        <div class="text-center mt-4 d-md-none">
            <button class="btn btn-outline-primary btn-sm" onclick="showAllPlatforms()">
                <i class="fas fa-plus me-2"></i>View All Platforms
            </button>
        </div>
    </div>
</section>

<!-- Simple Transparent Navigation -->
<nav class="navbar navbar-expand-lg fixed-top">
    <div class="container">
        <!-- Logo -->
        <a class="navbar-brand text-white fw-bold fs-4" href="#hero">
            <i class="fas fa-star me-2"></i>1xreviews
        </a>
        
        <!-- Hamburger Menu -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <i class="fas fa-bars text-white"></i>
        </button>
        
        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#hero">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#about">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#services">Services</a>
                </li>
            </ul>
            
            <!-- Buttons (Hidden on Mobile) -->
            <div class="d-none d-lg-flex gap-2 ms-auto">
                <button class="btn btn-outline-light rounded-pill px-3 py-2" onclick="showLoginModal('reviewer')">
                    Login
                </button>
                <button class="btn btn-light rounded-pill px-3 py-2" onclick="showLoginModal('customer')">
                    Sign Up
                </button>
            </div>
        </div>
    </div>
</nav>

<style>
.navbar {
    background: transparent !important;
    z-index: 1051;
    padding: 1rem 0;
    transition: background 0.3s;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    color: #fff !important;
    font-weight: 700;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler i {
    color: #fff !important;
    font-size: 1.5rem;
}

.nav-link {
    color: #fff !important;
    font-weight: 500;
    transition: opacity 0.2s;
}

.nav-link:hover {
    opacity: 0.8;
}

.btn {
    font-weight: 600;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        margin-top: 1rem;
        padding: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .nav-item {
        margin: 0.5rem 0;
    }
    
    /* Show buttons in mobile menu */
    .d-none.d-lg-flex {
        display: flex !important;
        flex-direction: column;
        width: 100%;
        margin-top: 1rem;
    }
    
    .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* Mobile-optimized platform styles */
@media (max-width: 768px) {
    .platforms-section {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }
    
    .section-title {
        font-size: 1.75rem !important;
        margin-bottom: 1rem !important;
    }
    
    .section-description {
        font-size: 0.9rem !important;
        margin-bottom: 2rem !important;
    }
    
    .platform-card-modern {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    .platform-logo-modern {
        width: 48px !important;
        height: 48px !important;
        margin-bottom: 0.75rem !important;
    }
    
    .platform-logo-modern img,
    .platform-logo-modern svg {
        width: 32px !important;
        height: 32px !important;
    }
    
    .platform-name-modern {
        font-size: 0.875rem !important;
        margin-bottom: 0 !important;
    }
}

@media (max-width: 576px) {
    .platforms-section {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }
    
    .section-title {
        font-size: 1.5rem !important;
    }
    
    .platform-card-modern {
        padding: 0.75rem !important;
        border-radius: 8px !important;
    }
    
    .platform-logo-modern {
        width: 40px !important;
        height: 40px !important;
        margin-bottom: 0.5rem !important;
    }
    
    .platform-logo-modern img,
    .platform-logo-modern svg {
        width: 28px !important;
        height: 28px !important;
    }
    
    .platform-name-modern {
        font-size: 0.8rem !important;
    }
}

/* Enhanced spacing and margins */
.platforms-section {
    background: #f8fafc;
    margin: 0;
}

.platform-card-modern {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.platform-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.platform-logo-modern {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    border-radius: 8px;
    background: #f8fafc;
}

.platform-logo-modern img,
.platform-logo-modern svg {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.platform-name-modern {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

/* Section spacing improvements */
.section-badge {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}
</style>

<script>
// Function to show all platforms on mobile
function showAllPlatforms() {
    // Show all hidden platform cards
    document.querySelectorAll('.platform-card-modern').forEach(card => {
        card.parentElement.classList.remove('d-none', 'd-md-block');
        card.parentElement.classList.add('col-4', 'col-md-4', 'col-lg-3');
    });
    
    // Hide the "View More" button
    document.querySelector('.btn-outline-primary').style.display = 'none';
}

// Add scroll effect to navbar
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});
</script> 
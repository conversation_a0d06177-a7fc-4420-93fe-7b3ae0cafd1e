<!-- Hero Section -->
<section class="hero-section py-4 py-md-6 d-flex align-items-center min-vh-75" id="hero">
    <div class="hero-background">
        <video autoplay loop muted playsinline class="hero-bg-video">
            <source src="assets/1.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>
    <div class="container position-relative z-2">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <h1 class="hero-title display-4 display-md-2 fw-bold mb-4 animate-fade-in-up minimalist-hero-title">Our reviewers will take it from here</h1>
                <!-- Website description -->
                <p class="hero-description mb-4 animate-fade-in-up">1xreviews connects businesses with real, verified reviewers to boost your online reputation on the world's most trusted platforms. Fast, secure, and transparent review services for Google, Trustpilot, Clutch, and more.</p>
                <!-- Service badges -->
                <div class="service-badges d-flex justify-content-center gap-3 gap-md-4 mb-4 animate-fade-in-up">
                    <span class="platform-badge"><i class="fab fa-google"></i> Google</span>
                    <span class="platform-badge"><i class="fas fa-thumbs-up"></i> Trustpilot</span>
                    <span class="platform-badge"><i class="fas fa-briefcase"></i> Clutch</span>
                    <span class="platform-badge"><i class="fab fa-youtube"></i> YouTube</span>
                </div>
                <!-- Main action buttons: always visible -->
                <div class="hero-main-actions d-flex flex-row flex-wrap justify-content-center gap-3 gap-md-4 mb-4 animate-fade-in-up">
                    <button class="btn btn-primary btn-lg px-4 px-md-5 py-3 fw-bold hero-action-btn">
                        <i class="fas fa-store me-2"></i>I NEED REVIEWS
                    </button>
                    <button class="btn btn-outline-light btn-lg px-4 px-md-5 py-3 fw-bold hero-action-btn">
                        <i class="fas fa-star me-2"></i>I WANT TO DO REVIEWS
                    </button>
                </div>
                <!-- Trusted by section: visible on desktop, hidden on mobile -->
                <div class="trusted-by d-flex flex-wrap justify-content-center align-items-center gap-3 gap-md-4 mt-3 mt-md-4 animate-fade-in-up mobile-hide">
                    <span class="trusted-label text-white-50 me-2 me-md-3 small">Trusted by:</span>
                    <div class="d-flex gap-3 gap-md-4">
                        <span class="trusted-logo"><i class="fab fa-meta fa-lg fa-md-2x"></i></span>
                        <span class="trusted-logo"><i class="fab fa-google fa-lg fa-md-2x"></i></span>
                        <span class="trusted-logo"><i class="fab fa-youtube fa-lg fa-md-2x"></i></span>
                        <span class="trusted-logo d-none d-md-inline"><i class="fab fa-paypal fa-lg fa-md-2x"></i></span>
                        <span class="trusted-logo d-none d-md-inline"><i class="fab fa-cc-visa fa-lg fa-md-2x"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Minimalist hero title */
.minimalist-hero-title {
    color: #fff !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    text-shadow: none !important;
    padding-top: 120px !important;
    padding-bottom: 1rem;
}
.hero-description {
    color: #fff;
    font-size: 1.15rem;
    opacity: 0.92;
}
.service-badges {
    margin-bottom: 2rem;
}
.platform-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-radius: 20px;
    padding: 0.5rem 1.25rem;
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: background 0.2s;
}
.platform-badge i {
    font-size: 1.2rem;
}
.platform-badge:hover {
    background: rgba(255,255,255,0.18);
}
@media (max-width: 768px) {
    .minimalist-hero-title {
        padding-top: 120px !important;
        font-size: 2rem !important;
    }
    .mobile-hide {
        display: none !important;
    }
    .hero-main-actions {
        flex-direction: column !important;
        gap: 1rem !important;
    }
    .service-badges {
        gap: 1rem !important;
    }
    .platform-badge {
        font-size: 0.95rem;
        padding: 0.4rem 1rem;
    }
}
@media (max-width: 576px) {
    .minimalist-hero-title {
        padding-top: 100px !important;
        font-size: 1.5rem !important;
    }
    .hero-description {
        font-size: 1rem;
    }
    .platform-badge {
        font-size: 0.85rem;
        padding: 0.3rem 0.7rem;
    }
}
.hero-main-actions {
    flex-direction: row !important;
    justify-content: center !important;
    gap: 1rem !important;
}
.hero-action-btn {
    min-width: 180px;
    font-size: 1rem;
}
</style> 
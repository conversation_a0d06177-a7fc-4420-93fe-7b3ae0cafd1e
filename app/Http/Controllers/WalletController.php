<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\WalletTransaction;
use App\Models\Withdrawal;

class WalletController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        $transactions = WalletTransaction::where('user_id', $user->id)
                                        ->orderBy('created_at', 'desc')
                                        ->paginate(10);

        return view('wallet.index', compact('user', 'transactions'));
    }

    public function withdrawals()
    {
        $user = Auth::user();
        
        $withdrawals = Withdrawal::where('user_id', $user->id)
                                ->orderBy('created_at', 'desc')
                                ->paginate(10);

        return view('wallet.withdrawals', compact('user', 'withdrawals'));
    }

    public function withdraw(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'amount' => 'required|numeric|min:10',
            'payment_method' => 'required|in:bank,paypal,crypto',
            'payment_details' => 'required|string'
        ]);

        if ($user->wallet_balance < $request->amount) {
            return redirect()->back()->with('error', 'Insufficient balance');
        }

        try {
            Withdrawal::create([
                'user_id' => $user->id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_details' => $request->payment_details,
                'status' => 'pending'
            ]);

            $user->decrement('wallet_balance', $request->amount);

            return redirect()->back()->with('success', 'Withdrawal request submitted successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error submitting withdrawal request');
        }
    }
} 
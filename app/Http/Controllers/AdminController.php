<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Review;
use App\Models\WalletTransaction;
use App\Models\Withdrawal;

class AdminController extends Controller
{
    public function dashboard()
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return redirect('/');
        }

        // Get all reviews
        $reviews = Review::with(['customer', 'reviewer'])
                        ->orderBy('created_at', 'desc')
                        ->get();

        // Get all users
        $users = User::orderBy('created_at', 'desc')->get();

        // Calculate stats
        $stats = [
            'total_reviews' => $reviews->count(),
            'pending_reviews' => $reviews->whereIn('status', ['open', 'claimed', 'submitted'])->count(),
            'total_users' => $users->count(),
            'total_revenue' => $reviews->where('status', 'completed')->sum('total_amount')
        ];

        return view('admin.dashboard', compact('user', 'reviews', 'users', 'stats'));
    }

    public function users()
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return redirect('/');
        }

        $users = User::orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users', compact('user', 'users'));
    }

    public function reviews()
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return redirect('/');
        }

        $reviews = Review::with(['customer', 'reviewer'])
                        ->orderBy('created_at', 'desc')
                        ->paginate(20);

        return view('admin.reviews', compact('user', 'reviews'));
    }

    public function withdrawals()
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return redirect('/');
        }

        $withdrawals = Withdrawal::with('user')
                                ->orderBy('created_at', 'desc')
                                ->paginate(20);

        return view('admin.withdrawals', compact('user', 'withdrawals'));
    }

    public function reviewDetails($id)
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $review = Review::with(['customer', 'reviewer'])
                       ->where('id', $id)
                       ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review not found']);
        }

        $html = view('admin.review_details', compact('review'))->render();
        
        return response()->json(['success' => true, 'html' => $html]);
    }

    public function approveReview($id)
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $review = Review::where('id', $id)
                       ->where('status', 'submitted')
                       ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review not found or not submitted']);
        }

        try {
            DB::beginTransaction();

            $review->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            // Pay the reviewer
            $reviewer = $review->reviewer;
            if ($reviewer) {
                $reviewer->increment('wallet_balance', $review->total_amount);
                
                WalletTransaction::create([
                    'user_id' => $reviewer->id,
                    'type' => 'credit',
                    'amount' => $review->total_amount,
                    'description' => "Payment for completed review #{$review->id}",
                    'reference_id' => $review->id,
                    'reference_type' => 'review',
                    'status' => 'completed'
                ]);
            }

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Review approved successfully!']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => 'Error approving review']);
        }
    }

    public function rejectReview($id, Request $request)
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }

        $review = Review::where('id', $id)
                       ->where('status', 'submitted')
                       ->first();

        if (!$review) {
            return response()->json(['success' => false, 'message' => 'Review not found or not submitted']);
        }

        try {
            DB::beginTransaction();

            $review->update([
                'status' => 'rejected',
                'rejection_reason' => $request->reason
            ]);

            // Refund the customer
            $customer = $review->customer;
            if ($customer) {
                $customer->increment('wallet_balance', $review->total_amount);
                
                WalletTransaction::create([
                    'user_id' => $customer->id,
                    'type' => 'credit',
                    'amount' => $review->total_amount,
                    'description' => "Refund for rejected review #{$review->id}",
                    'reference_id' => $review->id,
                    'reference_type' => 'review',
                    'status' => 'completed'
                ]);
            }

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Review rejected successfully!']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => 'Error rejecting review']);
        }
    }

    public function settings()
    {
        $user = Auth::user();
        
        if ($user->role !== 'admin') {
            return redirect('/');
        }

        return view('admin.settings', compact('user'));
    }
} 
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Models\Review;
use App\Models\User;

class ReviewController extends Controller
{
    public function create()
    {
        return view('reviews.create');
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'platform' => 'required|string',
            'business_name' => 'required|string|max:255',
            'business_url' => 'required|url',
            'review_text' => 'required|string|min:50',
            'screenshot' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'upi_qr' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048'
        ]);

        try {
            // Handle screenshot upload
            $screenshotPath = $request->file('screenshot')->store('reviews/screenshots', 'public');
            
            // Handle UPI QR upload
            $upiQrPath = $request->file('upi_qr')->store('reviews/upi_qr', 'public');

            Review::create([
                'user_id' => $user->id,
                'platform' => $request->platform,
                'business_name' => $request->business_name,
                'business_url' => $request->business_url,
                'review_text' => $request->review_text,
                'screenshot_path' => $screenshotPath,
                'upi_qr_path' => $upiQrPath,
                'status' => 'pending',
                'amount' => 50 // Default amount
            ]);

            return response()->json(['success' => true, 'message' => 'Review submitted successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error submitting review']);
        }
    }

    public function myReviews()
    {
        $user = Auth::user();
        
        $reviews = Review::where('user_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return view('reviews.my-reviews', compact('reviews'));
    }

    public function available()
    {
        $reviews = Review::where('status', 'pending')
                        ->where('claimed_by', null)
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return view('reviews.available', compact('reviews'));
    }

    public function claimed()
    {
        $user = Auth::user();
        
        $reviews = Review::where('claimed_by', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return view('reviews.claimed', compact('reviews'));
    }

    public function claimedReviews()
    {
        $user = Auth::user();
        
        $reviews = Review::where('claimed_by', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return view('reviews.claimed-reviews', compact('reviews'));
    }

    public function claim($id)
    {
        $user = Auth::user();
        $review = Review::findOrFail($id);

        if ($review->status !== 'pending' || $review->claimed_by !== null) {
            return response()->json(['success' => false, 'message' => 'Review not available for claiming']);
        }

        try {
            $review->update([
                'claimed_by' => $user->id,
                'claimed_at' => now()
            ]);

            return response()->json(['success' => true, 'message' => 'Review claimed successfully!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error claiming review']);
        }
    }
} 
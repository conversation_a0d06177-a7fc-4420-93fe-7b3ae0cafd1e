<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ContactController extends Controller
{
    public function index()
    {
        return view('contact');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|min:10'
        ]);

        // Here you would typically send an email or store the contact form data
        // For now, we'll just redirect with a success message
        
        return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }
} 
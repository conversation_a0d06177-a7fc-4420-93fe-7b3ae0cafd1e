<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Debug - Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .google-signin-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
        }
        .config-section {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .url-list {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Google OAuth Debug Tool</h1>
        
        <div class="config-section">
            <h3>Current Configuration</h3>
            <strong>URL:</strong> <span id="current-url"></span><br>
            <strong>Origin:</strong> <span id="origin"></span><br>
            <strong>Protocol:</strong> <span id="protocol"></span><br>
            <strong>Host:</strong> <span id="host"></span><br>
            <strong>Port:</strong> <span id="port"></span><br>
            <strong>Client ID:</strong> <span id="client-id"></span>
        </div>

        <div class="warning">
            <h3>🚨 Current Error: redirect_uri_mismatch</h3>
            <p>This means Google Cloud Console doesn't have the correct redirect URI configured.</p>
        </div>

        <div class="config-section">
            <h3>📋 Add These URLs to Google Cloud Console</h3>
            <h4>Authorized JavaScript Origins:</h4>
            <div class="url-list" id="js-origins"></div>
            
            <h4>Authorized Redirect URIs:</h4>
            <div class="url-list" id="redirect-uris"></div>
        </div>
        
        <div class="google-signin-container">
            <h3>Test Google Sign-In Button</h3>
            <div id="g_id_onload"
                data-client_id="668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com"
                data-callback="handleCredentialResponse"
                data-auto_prompt="false"
                data-ux_mode="popup">
            </div>
            <div id="g_id_signin" 
                 data-type="standard" 
                 data-size="large" 
                 data-theme="outline" 
                 data-text="sign_in_with" 
                 data-shape="rectangular" 
                 data-logo_alignment="left">
            </div>
        </div>
        
        <div id="status-display" class="status info">
            <strong>Status:</strong> Waiting for Google Sign-In library...
        </div>
        
        <div class="debug-info">
            <strong>Debug Log:</strong>
            <div id="debug-log"></div>
        </div>
    </div>

    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script>
        let debugLog = [];
        
        function addDebugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`${timestamp}: ${message}`);
            document.getElementById('debug-log').innerHTML = debugLog.join('<br>');
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
            statusDiv.className = 'status ' + type;
            addDebugLog(`Status: ${message}`);
        }
        
        // Update page info
        function updatePageInfo() {
            const url = window.location;
            document.getElementById('current-url').textContent = url.href;
            document.getElementById('origin').textContent = url.origin;
            document.getElementById('protocol').textContent = url.protocol;
            document.getElementById('host').textContent = url.hostname;
            document.getElementById('port').textContent = url.port || (url.protocol === 'https:' ? '443' : '80');
            document.getElementById('client-id').textContent = '668407029565-1ipn5a05amalu4lo6de4g1ge8e5sd8pm.apps.googleusercontent.com';
            
            // Generate the URLs that need to be added to Google Cloud Console
            const baseUrl = url.origin;
            const jsOrigins = [baseUrl];
            const redirectUris = [
                baseUrl,
                baseUrl + '/',
                baseUrl + '/auth/google/callback',
                baseUrl + '/google-login'
            ];
            
            document.getElementById('js-origins').innerHTML = jsOrigins.map(u => `• ${u}`).join('<br>');
            document.getElementById('redirect-uris').innerHTML = redirectUris.map(u => `• ${u}`).join('<br>');
            
            addDebugLog(`Page loaded: ${url.href}`);
            addDebugLog(`Origin: ${url.origin}`);
        }
        
        // Google Sign-In callback
        function handleCredentialResponse(response) {
            addDebugLog('Google Sign-In callback triggered');
            
            if (response.credential) {
                try {
                    const payload = JSON.parse(atob(response.credential.split('.')[1]));
                    updateStatus(`✅ Success! Signed in as: ${payload.email}`, 'success');
                    addDebugLog(`User: ${payload.email}`);
                    addDebugLog(`Name: ${payload.name}`);
                } catch (e) {
                    updateStatus(`❌ Error decoding response: ${e.message}`, 'error');
                    addDebugLog(`Decode error: ${e.message}`);
                }
            } else {
                updateStatus('❌ No credential received from Google', 'error');
                addDebugLog('No credential in response');
            }
        }
        
        // Monitor for Google library loading
        function checkGoogleLibrary() {
            if (typeof google !== 'undefined' && google.accounts) {
                updateStatus('✅ Google Sign-In library loaded successfully', 'success');
                addDebugLog('Google library available');
                return true;
            } else {
                updateStatus('⏳ Google Sign-In library still loading...', 'info');
                addDebugLog('Google library not yet available');
                return false;
            }
        }
        
        // Error monitoring
        window.addEventListener('error', function(e) {
            addDebugLog(`JavaScript error: ${e.message}`);
            if (e.message.includes('google') || e.message.includes('gsi')) {
                updateStatus(`❌ Google-related error: ${e.message}`, 'error');
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updatePageInfo();
            
            // Check for Google library every 2 seconds
            const checkInterval = setInterval(() => {
                if (checkGoogleLibrary()) {
                    clearInterval(checkInterval);
                }
            }, 2000);
            
            // Stop checking after 30 seconds
            setTimeout(() => {
                clearInterval(checkInterval);
                if (typeof google === 'undefined') {
                    updateStatus('❌ Google Sign-In library failed to load', 'error');
                }
            }, 30000);
        });
        
        addDebugLog('Debug tool initialized');
    </script>
</body>
</html>
